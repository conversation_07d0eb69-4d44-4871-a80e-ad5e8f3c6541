var kl=Object.defineProperty;var El=(a,o,l)=>o in a?kl(a,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[o]=l;var A=(a,o,l)=>El(a,typeof o!="symbol"?o+"":o,l);import{c as jl,f as ql,T as vt,A as Ol,a as Pl,p as Fl,W as Ul,b as xe,d as xo,g as Ll,r as Ro,s as zl,e as Ao,S as Wl,h as Dl}from"./rules-parser-D8-cU5vK.js";import{W as T}from"./BaseButton-Bbk8_XKh.js";import{a as $l,C as To,P as vr}from"./chat-types-NgqNgjwU.js";import{n as Bl}from"./file-paths-BcSg4gks.js";import{I as _t}from"./SpinnerAugment-BGEGncoZ.js";var xn;function Mo(a){const o=xn[a];return typeof o!="string"?a.toString():o[0].toLowerCase()+o.substring(1).replace(/[A-Z]/g,l=>"_"+l.toLowerCase())}(function(a){a[a.Canceled=1]="Canceled",a[a.Unknown=2]="Unknown",a[a.InvalidArgument=3]="InvalidArgument",a[a.DeadlineExceeded=4]="DeadlineExceeded",a[a.NotFound=5]="NotFound",a[a.AlreadyExists=6]="AlreadyExists",a[a.PermissionDenied=7]="PermissionDenied",a[a.ResourceExhausted=8]="ResourceExhausted",a[a.FailedPrecondition=9]="FailedPrecondition",a[a.Aborted=10]="Aborted",a[a.OutOfRange=11]="OutOfRange",a[a.Unimplemented=12]="Unimplemented",a[a.Internal=13]="Internal",a[a.Unavailable=14]="Unavailable",a[a.DataLoss=15]="DataLoss",a[a.Unauthenticated=16]="Unauthenticated"})(xn||(xn={}));class Sn extends Error{constructor(o,l=xn.Unknown,d,b,x){super(function(w,F){return w.length?`[${Mo(F)}] ${w}`:`[${Mo(F)}]`}(o,l)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=o,this.code=l,this.metadata=new Headers(d??{}),this.details=b??[],this.cause=x}static from(o,l=xn.Unknown){return o instanceof Sn?o:o instanceof Error?o.name=="AbortError"?new Sn(o.message,xn.Canceled):new Sn(o.message,l,void 0,void 0,o):new Sn(String(o),l,void 0,void 0,o)}static[Symbol.hasInstance](o){return o instanceof Error&&(Object.getPrototypeOf(o)===Sn.prototype||o.name==="ConnectError"&&"code"in o&&typeof o.code=="number"&&"metadata"in o&&"details"in o&&Array.isArray(o.details)&&"rawMessage"in o&&typeof o.rawMessage=="string"&&"cause"in o)}findDetails(o){const l=o.kind==="message"?{getMessage:b=>b===o.typeName?o:void 0}:o,d=[];for(const b of this.details){if("desc"in b){l.getMessage(b.desc.typeName)&&d.push(jl(b.desc,b.value));continue}const x=l.getMessage(b.type);if(x)try{d.push(ql(x,b.value))}catch{}}return d}}var Nl=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,l=a[Symbol.asyncIterator];return l?l.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),o={},d("next"),d("throw"),d("return"),o[Symbol.asyncIterator]=function(){return this},o);function d(b){o[b]=a[b]&&function(x){return new Promise(function(w,F){(function(G,re,oe,U){Promise.resolve(U).then(function(N){G({value:N,done:oe})},re)})(w,F,(x=a[b](x)).done,x.value)})}}},wt=function(a){return this instanceof wt?(this.v=a,this):new wt(a)},Hl=function(a,o,l){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var d,b=l.apply(a,o||[]),x=[];return d=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),w("next"),w("throw"),w("return",function(U){return function(N){return Promise.resolve(N).then(U,re)}}),d[Symbol.asyncIterator]=function(){return this},d;function w(U,N){b[U]&&(d[U]=function(se){return new Promise(function(ie,Re){x.push([U,se,ie,Re])>1||F(U,se)})},N&&(d[U]=N(d[U])))}function F(U,N){try{(se=b[U](N)).value instanceof wt?Promise.resolve(se.value.v).then(G,re):oe(x[0][2],se)}catch(ie){oe(x[0][3],ie)}var se}function G(U){F("next",U)}function re(U){F("throw",U)}function oe(U,N){U(N),x.shift(),x.length&&F(x[0][0],x[0][1])}},Gl=function(a){var o,l;return o={},d("next"),d("throw",function(b){throw b}),d("return"),o[Symbol.iterator]=function(){return this},o;function d(b,x){o[b]=a[b]?function(w){return(l=!l)?{value:wt(a[b](w)),done:!1}:x?x(w):w}:x}},ko=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,l=a[Symbol.asyncIterator];return l?l.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),o={},d("next"),d("throw"),d("return"),o[Symbol.asyncIterator]=function(){return this},o);function d(b){o[b]=a[b]&&function(x){return new Promise(function(w,F){(function(G,re,oe,U){Promise.resolve(U).then(function(N){G({value:N,done:oe})},re)})(w,F,(x=a[b](x)).done,x.value)})}}},Jn=function(a){return this instanceof Jn?(this.v=a,this):new Jn(a)},Vl=function(a){var o,l;return o={},d("next"),d("throw",function(b){throw b}),d("return"),o[Symbol.iterator]=function(){return this},o;function d(b,x){o[b]=a[b]?function(w){return(l=!l)?{value:Jn(a[b](w)),done:!1}:x?x(w):w}:x}},Zl=function(a,o,l){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var d,b=l.apply(a,o||[]),x=[];return d=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),w("next"),w("throw"),w("return",function(U){return function(N){return Promise.resolve(N).then(U,re)}}),d[Symbol.asyncIterator]=function(){return this},d;function w(U,N){b[U]&&(d[U]=function(se){return new Promise(function(ie,Re){x.push([U,se,ie,Re])>1||F(U,se)})},N&&(d[U]=N(d[U])))}function F(U,N){try{(se=b[U](N)).value instanceof Jn?Promise.resolve(se.value.v).then(G,re):oe(x[0][2],se)}catch(ie){oe(x[0][3],ie)}var se}function G(U){F("next",U)}function re(U){F("throw",U)}function oe(U,N){U(N),x.shift(),x.length&&F(x[0][0],x[0][1])}};function Kl(a,o){return function(l,d){const b={};for(const x of l.methods){const w=d(x);w!=null&&(b[x.localName]=w)}return b}(a,l=>{switch(l.methodKind){case"unary":return function(d,b){return async function(x,w){var F,G;const re=await d.unary(b,w==null?void 0:w.signal,w==null?void 0:w.timeoutMs,w==null?void 0:w.headers,x,w==null?void 0:w.contextValues);return(F=w==null?void 0:w.onHeader)===null||F===void 0||F.call(w,re.header),(G=w==null?void 0:w.onTrailer)===null||G===void 0||G.call(w,re.trailer),re.message}}(o,l);case"server_streaming":return function(d,b){return function(x,w){return Io(d.stream(b,w==null?void 0:w.signal,w==null?void 0:w.timeoutMs,w==null?void 0:w.headers,function(F){return Hl(this,arguments,function*(){yield wt(yield*Gl(Nl(F)))})}([x]),w==null?void 0:w.contextValues),w)}}(o,l);case"client_streaming":return function(d,b){return async function(x,w){var F,G,re,oe,U,N;const se=await d.stream(b,w==null?void 0:w.signal,w==null?void 0:w.timeoutMs,w==null?void 0:w.headers,x,w==null?void 0:w.contextValues);let ie;(U=w==null?void 0:w.onHeader)===null||U===void 0||U.call(w,se.header);let Re=0;try{for(var Y,Be=!0,fn=ko(se.message);!(F=(Y=await fn.next()).done);Be=!0)oe=Y.value,Be=!1,ie=oe,Re++}catch(On){G={error:On}}finally{try{Be||F||!(re=fn.return)||await re.call(fn)}finally{if(G)throw G.error}}if(!ie)throw new Sn("protocol error: missing response message",xn.Unimplemented);if(Re>1)throw new Sn("protocol error: received extra messages for client streaming method",xn.Unimplemented);return(N=w==null?void 0:w.onTrailer)===null||N===void 0||N.call(w,se.trailer),ie}}(o,l);case"bidi_streaming":return function(d,b){return function(x,w){return Io(d.stream(b,w==null?void 0:w.signal,w==null?void 0:w.timeoutMs,w==null?void 0:w.headers,x,w==null?void 0:w.contextValues),w)}}(o,l);default:return null}})}function Io(a,o){const l=function(){return Zl(this,arguments,function*(){var d,b;const x=yield Jn(a);(d=o==null?void 0:o.onHeader)===null||d===void 0||d.call(o,x.header),yield Jn(yield*Vl(ko(x.message))),(b=o==null?void 0:o.onTrailer)===null||b===void 0||b.call(o,x.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>l.next()})}}const mf="augment-welcome";var Te=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(Te||{}),Jl=(a=>(a.running="running",a.awaitingUserAction="awaiting-user-action",a.notRunning="not-running",a))(Jl||{}),Ke=(a=>(a.seen="seen",a.unseen="unseen",a))(Ke||{}),Yl=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(Yl||{});function Xl(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function wf(a){return Xl(a)&&a.status==="success"}function bf(a){return a.chatItemType==="autofix-message"}function Sf(a){return a.chatItemType==="autofix-steering-message"}function xf(a){return a.chatItemType==="autofix-stage"}function Rf(a){return a.chatItemType==="sign-in-welcome"}function Af(a){return a.chatItemType==="generate-commit-message"}function Mf(a){return a.chatItemType==="summary-response"}function If(a){return a.chatItemType==="educate-features"}function Cf(a){return a.chatItemType==="agent-onboarding"}function Tf(a){return a.chatItemType==="agentic-turn-delimiter"}function kf(a){return a.chatItemType==="agentic-checkpoint-delimiter"}function Ef(a){return a.revertTarget!==void 0}function jf(a){var o;return((o=a.structured_output_nodes)==null?void 0:o.some(l=>l.type===To.TOOL_USE))??!1}function qf(a){var o;return((o=a.structured_request_nodes)==null?void 0:o.some(l=>l.type===$l.TOOL_RESULT))??!1}function Of(a){return!(!a||typeof a!="object")&&(!("request_id"in a)||typeof a.request_id=="string")&&(!("seen_state"in a)||a.seen_state==="seen"||a.seen_state==="unseen")}async function*Ql(a,o=1e3){for(;a>0;)yield a,await new Promise(l=>setTimeout(l,Math.min(o,a))),a-=o}class ef{constructor(o,l,d,b=5,x=4e3,w){A(this,"_isCancelled",!1);this.requestId=o,this.chatMessage=l,this.startStreamFn=d,this.maxRetries=b,this.baseDelay=x,this.flags=w}cancel(){this._isCancelled=!0}async*getStream(){let o=0,l=!1;try{for(;!this._isCancelled;){const d=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let b,x=!1,w="";for await(const G of d){if(G.status===Te.failed){if(G.isRetriable!==!0||l)return yield G;x=!0,w=G.display_error_message||"Service is currently unavailable",b=G.request_id;break}l=!0,yield G}if(!x)return;if(this._isCancelled)return yield this.createCancelledStatus();if(o++,o>this.maxRetries)return void(yield{request_id:b??this.requestId,seen_state:Ke.unseen,status:Te.failed,display_error_message:w,isRetriable:!1});const F=this.baseDelay*2**(o-1);for await(const G of Ql(F))yield{request_id:this.requestId,status:Te.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(G/1e3)} seconds... (Attempt ${o} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:Te.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(d){yield{request_id:this.requestId,seen_state:Ke.unseen,status:Te.failed,display_error_message:d instanceof Error?d.message:String(d)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:Ke.unseen,status:Te.cancelled}}}class nf{constructor(o){A(this,"getHydratedTask",async o=>{const l={type:vt.getHydratedTaskRequest,data:{uuid:o}};return(await this._asyncMsgSender.sendToSidecar(l,3e4)).data.task});A(this,"createTask",async(o,l,d)=>{const b={type:vt.createTaskRequest,data:{name:o,description:l,parentTaskUuid:d}};return(await this._asyncMsgSender.sendToSidecar(b,3e4)).data.uuid});A(this,"updateTask",async(o,l,d)=>{const b={type:vt.updateTaskRequest,data:{uuid:o,updates:l,updatedBy:d}};await this._asyncMsgSender.sendToSidecar(b,3e4)});A(this,"setCurrentRootTaskUuid",o=>{const l={type:vt.setCurrentRootTaskUuid,data:{uuid:o}};this._asyncMsgSender.sendToSidecar(l)});A(this,"updateHydratedTask",async(o,l)=>{const d={type:vt.updateHydratedTaskRequest,data:{task:o,updatedBy:l}};return(await this._asyncMsgSender.sendToSidecar(d,3e4)).data});this._asyncMsgSender=o}}function ke(a,o){return o in a&&a[o]!==void 0}function tf(a){return ke(a,"file")}function rf(a){return ke(a,"recentFile")}function af(a){return ke(a,"folder")}function uf(a){return ke(a,"sourceFolder")}function Pf(a){return ke(a,"sourceFolderGroup")}function Ff(a){return ke(a,"selection")}function of(a){return ke(a,"externalSource")}function Uf(a){return ke(a,"allDefaultContext")}function Lf(a){return ke(a,"clearContext")}function zf(a){return ke(a,"userGuidelines")}function Wf(a){return ke(a,"agentMemories")}function sf(a){return ke(a,"personality")}function cf(a){return ke(a,"rule")}const Df={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},$f={clearContext:!0,label:"Clear Context",id:"clearContext"},Bf={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Nf={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Co=[{personality:{type:vr.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:vr.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:vr.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:vr.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Hf(a){return ke(a,"group")}function Gf(a){const o=new Map;return a.forEach(l=>{tf(l)?o.set("file",[...o.get("file")??[],l]):rf(l)?o.set("recentFile",[...o.get("recentFile")??[],l]):af(l)?o.set("folder",[...o.get("folder")??[],l]):of(l)?o.set("externalSource",[...o.get("externalSource")??[],l]):uf(l)?o.set("sourceFolder",[...o.get("sourceFolder")??[],l]):sf(l)?o.set("personality",[...o.get("personality")??[],l]):cf(l)&&o.set("rule",[...o.get("rule")??[],l])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:o.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:o.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:o.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:o.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:o.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:o.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:o.get("rule")??[]}}].filter(l=>l.group.items.length>0)}function lf(a){const o=Fl({rootPath:a.repoRoot,relPath:a.pathName}),l={label:Bl(a.pathName).split("/").filter(d=>d.trim()!=="").pop()||"",name:o,id:o};if(a.fullRange){const d=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;l.label+=d,l.name+=d,l.id+=d}else if(a.range){const d=`:L${a.range.start}-${a.range.stop}`;l.label+=d,l.name+=d,l.id+=d}return l}function ff(a){const o=a.path.split("/"),l=o[o.length-1],d=l.endsWith(".md")?l.slice(0,-3):l,b=`${Ol}/${Pl}/${a.path}`;return{label:d,name:b,id:b}}class Vf{constructor(o,l,d){A(this,"_taskClient");A(this,"getChatInitData",async()=>{const o=await this._asyncMsgSender.send({type:T.chatLoaded},3e4);if(o.data.enableDebugFeatures)try{console.log("Running hello world test...");const l=await async function(d){return(await Kl(Dl,new Wl({sendMessage:x=>{d.postMessage(x)},onReceiveMessage:x=>{const w=F=>{x(F.data)};return window.addEventListener("message",w),()=>{window.removeEventListener("message",w)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",l)}catch(l){console.error("Hello world error:",l)}return o.data});A(this,"reportWebviewClientEvent",o=>{this._asyncMsgSender.send({type:T.reportWebviewClientMetric,data:{webviewName:Ul.chat,client_metric:o,value:1}})});A(this,"reportAgentSessionEvent",o=>{this._asyncMsgSender.sendToSidecar({type:xe.reportAgentSessionEvent,data:o})});A(this,"reportAgentRequestEvent",o=>{this._asyncMsgSender.sendToSidecar({type:xe.reportAgentRequestEvent,data:o})});A(this,"getSuggestions",async(o,l=!1)=>{const d={rootPath:"",relPath:o},b=this.findFiles(d,6),x=this.findRecentlyOpenedFiles(d,6),w=this.findFolders(d,3),F=this.findExternalSources(o,l),G=this.findRules(o,6),[re,oe,U,N,se]=await Promise.all([mt(b,[]),mt(x,[]),mt(w,[]),mt(F,[]),mt(G,[])]),ie=(Y,Be)=>({...lf(Y),[Be]:Y}),Re=[...re.map(Y=>ie(Y,"file")),...U.map(Y=>ie(Y,"folder")),...oe.map(Y=>ie(Y,"recentFile")),...N.map(Y=>({label:Y.name,name:Y.name,id:Y.id,externalSource:Y})),...se.map(Y=>({...ff(Y),rule:Y}))];if(this._flags.enablePersonalities){const Y=this.getPersonalities(o);Y.length>0&&Re.push(...Y)}return Re});A(this,"getPersonalities",o=>{if(!this._flags.enablePersonalities)return[];if(o==="")return Co;const l=o.toLowerCase();return Co.filter(d=>{const b=d.personality.description.toLowerCase(),x=d.label.toLowerCase();return b.includes(l)||x.includes(l)})});A(this,"sendAction",o=>{this._host.postMessage({type:T.mainPanelPerformAction,data:o})});A(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:T.showAugmentPanel})});A(this,"openConfirmationModal",async o=>(await this._asyncMsgSender.send({type:T.openConfirmationModal,data:o},1e9)).data.ok);A(this,"clearMetadataFor",o=>{this._host.postMessage({type:T.chatClearMetadata,data:o})});A(this,"resolvePath",async(o,l=void 0)=>{const d=await this._asyncMsgSender.send({type:T.resolveFileRequest,data:{...o,exactMatch:!0,maxResults:1,searchScope:l}},5e3);if(d.data)return d.data});A(this,"resolveSymbols",async(o,l)=>(await this._asyncMsgSender.send({type:T.findSymbolRequest,data:{query:o,searchScope:l}},3e4)).data);A(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:T.getDiagnosticsRequest},1e3)).data);A(this,"findFiles",async(o,l=12)=>(await this._asyncMsgSender.send({type:T.findFileRequest,data:{...o,maxResults:l}},5e3)).data);A(this,"findFolders",async(o,l=12)=>(await this._asyncMsgSender.send({type:T.findFolderRequest,data:{...o,maxResults:l}},5e3)).data);A(this,"findRecentlyOpenedFiles",async(o,l=12)=>(await this._asyncMsgSender.send({type:T.findRecentlyOpenedFilesRequest,data:{...o,maxResults:l}},5e3)).data);A(this,"findExternalSources",async(o,l=!1)=>this._flags.enableExternalSourcesInChat?l?[]:(await this._asyncMsgSender.send({type:T.findExternalSourcesRequest,data:{query:o,source_types:[]}},5e3)).data.sources??[]:[]);A(this,"findRules",async(o,l=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:T.getRulesListRequest,data:{query:o,maxResults:l}},5e3)).data:[]);A(this,"openFile",o=>{this._host.postMessage({type:T.openFile,data:o})});A(this,"saveFile",o=>this._host.postMessage({type:T.saveFile,data:o}));A(this,"loadFile",o=>this._host.postMessage({type:T.loadFile,data:o}));A(this,"openMemoriesFile",()=>{this._host.postMessage({type:T.openMemoriesFile})});A(this,"createFile",(o,l)=>{this._host.postMessage({type:T.chatCreateFile,data:{code:o,relPath:l}})});A(this,"openScratchFile",async(o,l="shellscript")=>{await this._asyncMsgSender.send({type:T.openScratchFileRequest,data:{content:o,language:l}},1e4)});A(this,"resolveWorkspaceFileChunk",async o=>{try{return(await this._asyncMsgSender.send({type:T.resolveWorkspaceFileChunkRequest,data:o},5e3)).data}catch{return}});A(this,"smartPaste",o=>{this._host.postMessage({type:T.chatSmartPaste,data:o})});A(this,"getHydratedTask",async o=>this._taskClient.getHydratedTask(o));A(this,"updateHydratedTask",async(o,l)=>this._taskClient.updateHydratedTask(o,l));A(this,"setCurrentRootTaskUuid",o=>{this._taskClient.setCurrentRootTaskUuid(o)});A(this,"createTask",async(o,l,d)=>this._taskClient.createTask(o,l,d));A(this,"updateTask",async(o,l,d)=>this._taskClient.updateTask(o,l,d));A(this,"saveChat",async(o,l,d)=>this._asyncMsgSender.send({type:T.saveChat,data:{conversationId:o,chatHistory:l,title:d}}));A(this,"launchAutofixPanel",async(o,l,d)=>this._asyncMsgSender.send({type:T.chatLaunchAutofixPanel,data:{conversationId:o,iterationId:l,stage:d}}));A(this,"updateUserGuidelines",o=>{this._host.postMessage({type:T.updateUserGuidelines,data:o})});A(this,"updateWorkspaceGuidelines",o=>{this._host.postMessage({type:T.updateWorkspaceGuidelines,data:o})});A(this,"updateRuleFile",(o,l)=>{this._host.postMessage({type:T.updateRuleFile,data:{rulePath:o,content:l}})});A(this,"openSettingsPage",o=>{this._host.postMessage({type:T.openSettingsPage,data:o})});A(this,"_activeRetryStreams",new Map);A(this,"cancelChatStream",async o=>{var l;(l=this._activeRetryStreams.get(o))==null||l.cancel(),await this._asyncMsgSender.send({type:T.chatUserCancel,data:{requestId:o}},1e4)});A(this,"sendUserRating",async(o,l,d,b="")=>{const x={requestId:o,rating:d,note:b,mode:l},w={type:T.chatRating,data:x};return(await this._asyncMsgSender.send(w,3e4)).data});A(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:T.usedChat})});A(this,"createProject",o=>{this._host.postMessage({type:T.mainPanelCreateProject,data:{name:o}})});A(this,"openProjectFolder",()=>{this._host.postMessage({type:T.mainPanelPerformAction,data:"open-folder"})});A(this,"closeProjectFolder",()=>{this._host.postMessage({type:T.mainPanelPerformAction,data:"close-folder"})});A(this,"cloneRepository",()=>{this._host.postMessage({type:T.mainPanelPerformAction,data:"clone-repository"})});A(this,"grantSyncPermission",()=>{this._host.postMessage({type:T.mainPanelPerformAction,data:"grant-sync-permission"})});A(this,"callTool",async(o,l,d,b,x,w)=>{const F={type:T.callTool,data:{chatRequestId:o,toolUseId:l,name:d,input:b,chatHistory:x,conversationId:w}};return(await this._asyncMsgSender.send(F,0)).data});A(this,"cancelToolRun",async(o,l)=>{const d={type:T.cancelToolRun,data:{requestId:o,toolUseId:l}};await this._asyncMsgSender.send(d,0)});A(this,"checkSafe",async(o,l)=>{const d={type:T.toolCheckSafe,data:{name:o,input:l}};return(await this._asyncMsgSender.send(d,0)).data.isSafe});A(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:xo.closeAllToolProcesses},0)});A(this,"getToolIdentifier",async o=>{const l={type:xo.getToolIdentifierRequest,data:{toolName:o}};return(await this._asyncMsgSender.sendToSidecar(l,0)).data});A(this,"executeCommand",async(o,l,d)=>{try{const b=await this._asyncMsgSender.send({type:T.chatAutofixExecuteCommandRequest,data:{iterationId:o,command:l,args:d}},6e5);return{output:b.data.output,returnCode:b.data.returnCode}}catch(b){throw console.error("[ExtensionClient] Execute command failed:",b),b}});A(this,"sendAutofixStateUpdate",async o=>{await this._asyncMsgSender.send({type:T.chatAutofixStateUpdate,data:o})});A(this,"autofixPlan",async(o,l)=>(await this._asyncMsgSender.send({type:T.chatAutofixPlanRequest,data:{command:o,steeringHistory:l}},6e4)).data.plan);A(this,"setChatMode",o=>{this._asyncMsgSender.send({type:T.chatModeChanged,data:{mode:o}})});A(this,"getAgentEditList",async(o,l)=>{const d={type:xe.getEditListRequest,data:{fromTimestamp:o,toTimestamp:l}};return(await this._asyncMsgSender.sendToSidecar(d,3e4)).data});A(this,"hasChangesSince",async o=>{const l={type:xe.getEditListRequest,data:{fromTimestamp:o,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(l,3e4)).data.edits.filter(d=>{var b,x;return((b=d.changesSummary)==null?void 0:b.totalAddedLines)||((x=d.changesSummary)==null?void 0:x.totalRemovedLines)}).length>0});A(this,"getToolCallCheckpoint",async o=>{const l={type:T.getToolCallCheckpoint,data:{requestId:o}};return(await this._asyncMsgSender.send(l,3e4)).data.checkpointNumber});A(this,"setCurrentConversation",o=>{this._asyncMsgSender.sendToSidecar({type:xe.setCurrentConversation,data:{conversationId:o}})});A(this,"migrateConversationId",async(o,l)=>{await this._asyncMsgSender.sendToSidecar({type:xe.migrateConversationId,data:{oldConversationId:o,newConversationId:l}},3e4)});A(this,"showAgentReview",(o,l,d,b=!0)=>{this._asyncMsgSender.sendToSidecar({type:xe.chatReviewAgentFile,data:{qualifiedPathName:o,fromTimestamp:l,toTimestamp:d,retainFocus:b}})});A(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:xe.chatAgentEditAcceptAll}),!0));A(this,"revertToTimestamp",async(o,l)=>(await this._asyncMsgSender.sendToSidecar({type:xe.revertToTimestamp,data:{timestamp:o,qualifiedPathNames:l}}),!0));A(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:T.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);A(this,"getAgentEditChangesByRequestId",async o=>{const l={type:xe.getEditChangesByRequestIdRequest,data:{requestId:o}};return(await this._asyncMsgSender.sendToSidecar(l,3e4)).data});A(this,"getAgentEditContentsByRequestId",async o=>{const l={type:xe.getAgentEditContentsByRequestId,data:{requestId:o}};return(await this._asyncMsgSender.sendToSidecar(l,3e4)).data});A(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:T.triggerInitialOrientation})});A(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:T.getWorkspaceInfoRequest},5e3)).data}catch(o){return console.error("Error getting workspace info:",o),{}}});A(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:T.toggleCollapseUnchangedRegions})});A(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:T.checkAgentAutoModeApproval},5e3)).data);A(this,"setAgentAutoModeApproved",async o=>{await this._asyncMsgSender.send({type:T.setAgentAutoModeApproved,data:o},5e3)});A(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:xe.checkHasEverUsedAgent},5e3)).data);A(this,"setHasEverUsedAgent",async o=>{await this._asyncMsgSender.sendToSidecar({type:xe.setHasEverUsedAgent,data:o},5e3)});A(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:xe.checkHasEverUsedRemoteAgent},5e3)).data);A(this,"setHasEverUsedRemoteAgent",async o=>{await this._asyncMsgSender.sendToSidecar({type:xe.setHasEverUsedRemoteAgent,data:o},5e3)});A(this,"getChatRequestIdeState",async()=>{const o={type:T.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(o,3e4)).data});A(this,"reportError",o=>{this._host.postMessage({type:T.reportError,data:o})});this._host=o,this._asyncMsgSender=l,this._flags=d,this._taskClient=new nf(l)}async*generateCommitMessage(){const o={type:T.generateCommitMessage},l=this._asyncMsgSender.stream(o,3e4,6e4);yield*Ba(l)}async*sendInstructionMessage(o,l){const d={instruction:o.request_message??"",selectedCodeDetails:l,requestId:o.request_id},b={type:T.chatInstructionMessage,data:d},x=this._asyncMsgSender.stream(b,3e4,6e4);yield*async function*(w){let F;try{for await(const G of w)F=G.data.requestId,yield{request_id:F,response_text:G.data.text,seen_state:Ke.unseen,status:Te.sent};yield{request_id:F,seen_state:Ke.unseen,status:Te.success}}catch{yield{request_id:F,seen_state:Ke.unseen,status:Te.failed}}}(x)}async openGuidelines(o){this._host.postMessage({type:T.openGuidelines,data:o})}async*getExistingChatStream(o,l){if(!o.request_id)return;const d=l==null?void 0:l.flags.enablePreferenceCollection,b=d?1e9:6e4,x=d?1e9:3e5,w={type:T.chatGetStreamRequest,data:{requestId:o.request_id}},F=this._asyncMsgSender.stream(w,b,x);yield*Ba(F,this.reportError)}async*startChatStream(o,l){const d=l==null?void 0:l.flags.enablePreferenceCollection,b=d?1e9:6e4,x=d?1e9:3e5,w={type:T.chatUserMessage,data:o},F=this._asyncMsgSender.stream(w,b,x);yield*Ba(F,this.reportError)}async checkToolExists(o){return(await this._asyncMsgSender.send({type:T.checkToolExists,toolName:o},0)).exists}async saveImage(o,l){const d=Ll(await Ro(o)),b=l??`${await zl(await Ao(d))}.${o.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:T.chatSaveImageRequest,data:{filename:b,data:d}},1e4)).data}async loadImage(o){const l=await this._asyncMsgSender.send({type:T.chatLoadImageRequest,data:o},1e4),d=l.data?await Ao(l.data):void 0;if(!d)return;let b="application/octet-stream";const x=o.split(".").at(-1);x==="png"?b="image/png":x!=="jpg"&&x!=="jpeg"||(b="image/jpeg");const w=new File([d],o,{type:b});return await Ro(w)}async deleteImage(o){await this._asyncMsgSender.send({type:T.chatDeleteImageRequest,data:o},1e4)}async*startChatStreamWithRetry(o,l,d){const b=new ef(o,l,(x,w)=>this.startChatStream(x,w),(d==null?void 0:d.maxRetries)??5,4e3,d==null?void 0:d.flags);this._activeRetryStreams.set(o,b);try{yield*b.getStream()}finally{this._activeRetryStreams.delete(o)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:T.getSubscriptionInfo},5e3)}}async function*Ba(a,o=()=>{}){let l;try{for await(const d of a){if(l=d.data.requestId,d.data.error)return yield{request_id:l,seen_state:Ke.unseen,status:Te.failed,display_error_message:d.data.error.displayErrorMessage,isRetriable:d.data.error.isRetriable};yield{request_id:l,response_text:d.data.text,workspace_file_chunks:d.data.workspaceFileChunks,structured_output_nodes:hf(d.data.nodes),seen_state:Ke.unseen,status:Te.sent}}yield{request_id:l,seen_state:Ke.unseen,status:Te.success}}catch(d){o({originalRequestId:l||"",sanitizedMessage:d instanceof Error?d.message:String(d),stackTrace:d instanceof Error&&d.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:l,seen_state:Ke.unseen,status:Te.failed}}}async function mt(a,o){try{return await a}catch(l){return console.warn(`Error while resolving promise: ${l}`),o}}function hf(a){if(!a)return a;let o=!1;return a.filter(l=>l.type!==To.TOOL_USE||!o&&(o=!0,!0))}var _r,mr,Na={exports:{}};_r=Na,mr=Na.exports,(function(){var a,o="Expected a function",l="__lodash_hash_undefined__",d="__lodash_placeholder__",b=16,x=32,w=64,F=128,G=256,re=1/0,oe=9007199254740991,U=NaN,N=4294967295,se=[["ary",F],["bind",1],["bindKey",2],["curry",8],["curryRight",b],["flip",512],["partial",x],["partialRight",w],["rearg",G]],ie="[object Arguments]",Re="[object Array]",Y="[object Boolean]",Be="[object Date]",fn="[object Error]",On="[object Function]",Ha="[object GeneratorFunction]",Ne="[object Map]",Yn="[object Number]",nn="[object Object]",Ga="[object Promise]",Xn="[object RegExp]",He="[object Set]",Qn="[object String]",bt="[object Symbol]",et="[object WeakMap]",nt="[object ArrayBuffer]",Pn="[object DataView]",wr="[object Float32Array]",br="[object Float64Array]",Sr="[object Int8Array]",xr="[object Int16Array]",Rr="[object Int32Array]",Ar="[object Uint8Array]",Mr="[object Uint8ClampedArray]",Ir="[object Uint16Array]",Cr="[object Uint32Array]",Eo=/\b__p \+= '';/g,jo=/\b(__p \+=) '' \+/g,qo=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Va=/&(?:amp|lt|gt|quot|#39);/g,Za=/[&<>"']/g,Oo=RegExp(Va.source),Po=RegExp(Za.source),Fo=/<%-([\s\S]+?)%>/g,Uo=/<%([\s\S]+?)%>/g,Ka=/<%=([\s\S]+?)%>/g,Lo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,zo=/^\w*$/,Wo=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Tr=/[\\^$.*+?()[\]{}|]/g,Do=RegExp(Tr.source),kr=/^\s+/,$o=/\s/,Bo=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,No=/\{\n\/\* \[wrapped with (.+)\] \*/,Ho=/,? & /,Go=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Vo=/[()=,{}\[\]\/\s]/,Zo=/\\(\\)?/g,Ko=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Ja=/\w*$/,Jo=/^[-+]0x[0-9a-f]+$/i,Yo=/^0b[01]+$/i,Xo=/^\[object .+?Constructor\]$/,Qo=/^0o[0-7]+$/i,es=/^(?:0|[1-9]\d*)$/,ns=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,St=/($^)/,ts=/['\n\r\u2028\u2029\\]/g,xt="\\ud800-\\udfff",Ya="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Xa="\\u2700-\\u27bf",Qa="a-z\\xdf-\\xf6\\xf8-\\xff",ei="A-Z\\xc0-\\xd6\\xd8-\\xde",ni="\\ufe0e\\ufe0f",ti="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",rs="['’]",as="["+xt+"]",ri="["+ti+"]",Rt="["+Ya+"]",ai="\\d+",is="["+Xa+"]",ii="["+Qa+"]",ui="[^"+xt+ti+ai+Xa+Qa+ei+"]",Er="\\ud83c[\\udffb-\\udfff]",oi="[^"+xt+"]",jr="(?:\\ud83c[\\udde6-\\uddff]){2}",qr="[\\ud800-\\udbff][\\udc00-\\udfff]",Fn="["+ei+"]",si="\\u200d",ci="(?:"+ii+"|"+ui+")",us="(?:"+Fn+"|"+ui+")",li="(?:['’](?:d|ll|m|re|s|t|ve))?",fi="(?:['’](?:D|LL|M|RE|S|T|VE))?",hi="(?:"+Rt+"|"+Er+")?",di="["+ni+"]?",pi=di+hi+"(?:"+si+"(?:"+[oi,jr,qr].join("|")+")"+di+hi+")*",os="(?:"+[is,jr,qr].join("|")+")"+pi,ss="(?:"+[oi+Rt+"?",Rt,jr,qr,as].join("|")+")",cs=RegExp(rs,"g"),ls=RegExp(Rt,"g"),Or=RegExp(Er+"(?="+Er+")|"+ss+pi,"g"),fs=RegExp([Fn+"?"+ii+"+"+li+"(?="+[ri,Fn,"$"].join("|")+")",us+"+"+fi+"(?="+[ri,Fn+ci,"$"].join("|")+")",Fn+"?"+ci+"+"+li,Fn+"+"+fi,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",ai,os].join("|"),"g"),hs=RegExp("["+si+xt+Ya+ni+"]"),ds=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,ps=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],gs=-1,Q={};Q[wr]=Q[br]=Q[Sr]=Q[xr]=Q[Rr]=Q[Ar]=Q[Mr]=Q[Ir]=Q[Cr]=!0,Q[ie]=Q[Re]=Q[nt]=Q[Y]=Q[Pn]=Q[Be]=Q[fn]=Q[On]=Q[Ne]=Q[Yn]=Q[nn]=Q[Xn]=Q[He]=Q[Qn]=Q[et]=!1;var X={};X[ie]=X[Re]=X[nt]=X[Pn]=X[Y]=X[Be]=X[wr]=X[br]=X[Sr]=X[xr]=X[Rr]=X[Ne]=X[Yn]=X[nn]=X[Xn]=X[He]=X[Qn]=X[bt]=X[Ar]=X[Mr]=X[Ir]=X[Cr]=!0,X[fn]=X[On]=X[et]=!1;var ys={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},vs=parseFloat,_s=parseInt,gi=typeof _t=="object"&&_t&&_t.Object===Object&&_t,ms=typeof self=="object"&&self&&self.Object===Object&&self,ge=gi||ms||Function("return this")(),Pr=mr&&!mr.nodeType&&mr,Rn=Pr&&_r&&!_r.nodeType&&_r,yi=Rn&&Rn.exports===Pr,Fr=yi&&gi.process,Pe=function(){try{var p=Rn&&Rn.require&&Rn.require("util").types;return p||Fr&&Fr.binding&&Fr.binding("util")}catch{}}(),vi=Pe&&Pe.isArrayBuffer,_i=Pe&&Pe.isDate,mi=Pe&&Pe.isMap,wi=Pe&&Pe.isRegExp,bi=Pe&&Pe.isSet,Si=Pe&&Pe.isTypedArray;function Ee(p,_,m){switch(m.length){case 0:return p.call(_);case 1:return p.call(_,m[0]);case 2:return p.call(_,m[0],m[1]);case 3:return p.call(_,m[0],m[1],m[2])}return p.apply(_,m)}function ws(p,_,m,I){for(var z=-1,V=p==null?0:p.length;++z<V;){var fe=p[z];_(I,fe,m(fe),p)}return I}function Fe(p,_){for(var m=-1,I=p==null?0:p.length;++m<I&&_(p[m],m,p)!==!1;);return p}function bs(p,_){for(var m=p==null?0:p.length;m--&&_(p[m],m,p)!==!1;);return p}function xi(p,_){for(var m=-1,I=p==null?0:p.length;++m<I;)if(!_(p[m],m,p))return!1;return!0}function hn(p,_){for(var m=-1,I=p==null?0:p.length,z=0,V=[];++m<I;){var fe=p[m];_(fe,m,p)&&(V[z++]=fe)}return V}function At(p,_){return!(p==null||!p.length)&&Un(p,_,0)>-1}function Ur(p,_,m){for(var I=-1,z=p==null?0:p.length;++I<z;)if(m(_,p[I]))return!0;return!1}function te(p,_){for(var m=-1,I=p==null?0:p.length,z=Array(I);++m<I;)z[m]=_(p[m],m,p);return z}function dn(p,_){for(var m=-1,I=_.length,z=p.length;++m<I;)p[z+m]=_[m];return p}function Lr(p,_,m,I){var z=-1,V=p==null?0:p.length;for(I&&V&&(m=p[++z]);++z<V;)m=_(m,p[z],z,p);return m}function Ss(p,_,m,I){var z=p==null?0:p.length;for(I&&z&&(m=p[--z]);z--;)m=_(m,p[z],z,p);return m}function zr(p,_){for(var m=-1,I=p==null?0:p.length;++m<I;)if(_(p[m],m,p))return!0;return!1}var xs=Wr("length");function Ri(p,_,m){var I;return m(p,function(z,V,fe){if(_(z,V,fe))return I=V,!1}),I}function Mt(p,_,m,I){for(var z=p.length,V=m+(I?1:-1);I?V--:++V<z;)if(_(p[V],V,p))return V;return-1}function Un(p,_,m){return _==_?function(I,z,V){for(var fe=V-1,Je=I.length;++fe<Je;)if(I[fe]===z)return fe;return-1}(p,_,m):Mt(p,Ai,m)}function Rs(p,_,m,I){for(var z=m-1,V=p.length;++z<V;)if(I(p[z],_))return z;return-1}function Ai(p){return p!=p}function Mi(p,_){var m=p==null?0:p.length;return m?$r(p,_)/m:U}function Wr(p){return function(_){return _==null?a:_[p]}}function Dr(p){return function(_){return p==null?a:p[_]}}function Ii(p,_,m,I,z){return z(p,function(V,fe,Je){m=I?(I=!1,V):_(m,V,fe,Je)}),m}function $r(p,_){for(var m,I=-1,z=p.length;++I<z;){var V=_(p[I]);V!==a&&(m=m===a?V:m+V)}return m}function Br(p,_){for(var m=-1,I=Array(p);++m<p;)I[m]=_(m);return I}function Ci(p){return p&&p.slice(0,ji(p)+1).replace(kr,"")}function je(p){return function(_){return p(_)}}function Nr(p,_){return te(_,function(m){return p[m]})}function tt(p,_){return p.has(_)}function Ti(p,_){for(var m=-1,I=p.length;++m<I&&Un(_,p[m],0)>-1;);return m}function ki(p,_){for(var m=p.length;m--&&Un(_,p[m],0)>-1;);return m}var As=Dr({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),Ms=Dr({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function Is(p){return"\\"+ys[p]}function Ln(p){return hs.test(p)}function Hr(p){var _=-1,m=Array(p.size);return p.forEach(function(I,z){m[++_]=[z,I]}),m}function Ei(p,_){return function(m){return p(_(m))}}function pn(p,_){for(var m=-1,I=p.length,z=0,V=[];++m<I;){var fe=p[m];fe!==_&&fe!==d||(p[m]=d,V[z++]=m)}return V}function It(p){var _=-1,m=Array(p.size);return p.forEach(function(I){m[++_]=I}),m}function Cs(p){var _=-1,m=Array(p.size);return p.forEach(function(I){m[++_]=[I,I]}),m}function zn(p){return Ln(p)?function(_){for(var m=Or.lastIndex=0;Or.test(_);)++m;return m}(p):xs(p)}function Ge(p){return Ln(p)?function(_){return _.match(Or)||[]}(p):function(_){return _.split("")}(p)}function ji(p){for(var _=p.length;_--&&$o.test(p.charAt(_)););return _}var Ts=Dr({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Wn=function p(_){var m,I=(_=_==null?ge:Wn.defaults(ge.Object(),_,Wn.pick(ge,ps))).Array,z=_.Date,V=_.Error,fe=_.Function,Je=_.Math,ee=_.Object,Gr=_.RegExp,ks=_.String,Ue=_.TypeError,Ct=I.prototype,Es=fe.prototype,Dn=ee.prototype,Tt=_["__core-js_shared__"],kt=Es.toString,J=Dn.hasOwnProperty,js=0,qi=(m=/[^.]+$/.exec(Tt&&Tt.keys&&Tt.keys.IE_PROTO||""))?"Symbol(src)_1."+m:"",Et=Dn.toString,qs=kt.call(ee),Os=ge._,Ps=Gr("^"+kt.call(J).replace(Tr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),jt=yi?_.Buffer:a,gn=_.Symbol,qt=_.Uint8Array,Oi=jt?jt.allocUnsafe:a,Ot=Ei(ee.getPrototypeOf,ee),Pi=ee.create,Fi=Dn.propertyIsEnumerable,Pt=Ct.splice,Ui=gn?gn.isConcatSpreadable:a,rt=gn?gn.iterator:a,An=gn?gn.toStringTag:a,Ft=function(){try{var e=kn(ee,"defineProperty");return e({},"",{}),e}catch{}}(),Fs=_.clearTimeout!==ge.clearTimeout&&_.clearTimeout,Us=z&&z.now!==ge.Date.now&&z.now,Ls=_.setTimeout!==ge.setTimeout&&_.setTimeout,Ut=Je.ceil,Lt=Je.floor,Vr=ee.getOwnPropertySymbols,zs=jt?jt.isBuffer:a,Li=_.isFinite,Ws=Ct.join,Ds=Ei(ee.keys,ee),he=Je.max,ve=Je.min,$s=z.now,Bs=_.parseInt,zi=Je.random,Ns=Ct.reverse,Zr=kn(_,"DataView"),at=kn(_,"Map"),Kr=kn(_,"Promise"),$n=kn(_,"Set"),it=kn(_,"WeakMap"),ut=kn(ee,"create"),zt=it&&new it,Bn={},Hs=En(Zr),Gs=En(at),Vs=En(Kr),Zs=En($n),Ks=En(it),Wt=gn?gn.prototype:a,ot=Wt?Wt.valueOf:a,Wi=Wt?Wt.toString:a;function u(e){if(ue(e)&&!D(e)&&!(e instanceof H)){if(e instanceof Le)return e;if(J.call(e,"__wrapped__"))return Du(e)}return new Le(e)}var Nn=function(){function e(){}return function(n){if(!ae(n))return{};if(Pi)return Pi(n);e.prototype=n;var t=new e;return e.prototype=a,t}}();function Dt(){}function Le(e,n){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=a}function H(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=N,this.__views__=[]}function Mn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function tn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function rn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}function In(e){var n=-1,t=e==null?0:e.length;for(this.__data__=new rn;++n<t;)this.add(e[n])}function Ve(e){var n=this.__data__=new tn(e);this.size=n.size}function Di(e,n){var t=D(e),r=!t&&jn(e),i=!t&&!r&&wn(e),s=!t&&!r&&!i&&Zn(e),c=t||r||i||s,f=c?Br(e.length,ks):[],h=f.length;for(var y in e)!n&&!J.call(e,y)||c&&(y=="length"||i&&(y=="offset"||y=="parent")||s&&(y=="buffer"||y=="byteLength"||y=="byteOffset")||sn(y,h))||f.push(y);return f}function $i(e){var n=e.length;return n?e[ua(0,n-1)]:a}function Js(e,n){return nr(Ae(e),Cn(n,0,e.length))}function Ys(e){return nr(Ae(e))}function Jr(e,n,t){(t!==a&&!Ze(e[n],t)||t===a&&!(n in e))&&an(e,n,t)}function st(e,n,t){var r=e[n];J.call(e,n)&&Ze(r,t)&&(t!==a||n in e)||an(e,n,t)}function $t(e,n){for(var t=e.length;t--;)if(Ze(e[t][0],n))return t;return-1}function Xs(e,n,t,r){return yn(e,function(i,s,c){n(r,i,t(i),c)}),r}function Bi(e,n){return e&&Xe(n,pe(n),e)}function an(e,n,t){n=="__proto__"&&Ft?Ft(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t}function Yr(e,n){for(var t=-1,r=n.length,i=I(r),s=e==null;++t<r;)i[t]=s?a:Ea(e,n[t]);return i}function Cn(e,n,t){return e==e&&(t!==a&&(e=e<=t?e:t),n!==a&&(e=e>=n?e:n)),e}function ze(e,n,t,r,i,s){var c,f=1&n,h=2&n,y=4&n;if(t&&(c=i?t(e,r,i,s):t(e)),c!==a)return c;if(!ae(e))return e;var g=D(e);if(g){if(c=function(v){var R=v.length,O=new v.constructor(R);return R&&typeof v[0]=="string"&&J.call(v,"index")&&(O.index=v.index,O.input=v.input),O}(e),!f)return Ae(e,c)}else{var S=_e(e),C=S==On||S==Ha;if(wn(e))return du(e,f);if(S==nn||S==ie||C&&!i){if(c=h||C?{}:ju(e),!f)return h?function(v,R){return Xe(v,ku(v),R)}(e,function(v,R){return v&&Xe(R,Ie(R),v)}(c,e)):function(v,R){return Xe(v,wa(v),R)}(e,Bi(c,e))}else{if(!X[S])return i?e:{};c=function(v,R,O){var M,W=v.constructor;switch(R){case nt:return da(v);case Y:case Be:return new W(+v);case Pn:return function(L,Z){var E=Z?da(L.buffer):L.buffer;return new L.constructor(E,L.byteOffset,L.byteLength)}(v,O);case wr:case br:case Sr:case xr:case Rr:case Ar:case Mr:case Ir:case Cr:return pu(v,O);case Ne:return new W;case Yn:case Qn:return new W(v);case Xn:return function(L){var Z=new L.constructor(L.source,Ja.exec(L));return Z.lastIndex=L.lastIndex,Z}(v);case He:return new W;case bt:return M=v,ot?ee(ot.call(M)):{}}}(e,S,f)}}s||(s=new Ve);var k=s.get(e);if(k)return k;s.set(e,c),io(e)?e.forEach(function(v){c.add(ze(v,n,t,v,e,s))}):ro(e)&&e.forEach(function(v,R){c.set(R,ze(v,n,t,R,e,s))});var j=g?a:(y?h?va:ya:h?Ie:pe)(e);return Fe(j||e,function(v,R){j&&(v=e[R=v]),st(c,R,ze(v,n,t,R,e,s))}),c}function Ni(e,n,t){var r=t.length;if(e==null)return!r;for(e=ee(e);r--;){var i=t[r],s=n[i],c=e[i];if(c===a&&!(i in e)||!s(c))return!1}return!0}function Hi(e,n,t){if(typeof e!="function")throw new Ue(o);return gt(function(){e.apply(a,t)},n)}function ct(e,n,t,r){var i=-1,s=At,c=!0,f=e.length,h=[],y=n.length;if(!f)return h;t&&(n=te(n,je(t))),r?(s=Ur,c=!1):n.length>=200&&(s=tt,c=!1,n=new In(n));e:for(;++i<f;){var g=e[i],S=t==null?g:t(g);if(g=r||g!==0?g:0,c&&S==S){for(var C=y;C--;)if(n[C]===S)continue e;h.push(g)}else s(n,S,r)||h.push(g)}return h}u.templateSettings={escape:Fo,evaluate:Uo,interpolate:Ka,variable:"",imports:{_:u}},u.prototype=Dt.prototype,u.prototype.constructor=u,Le.prototype=Nn(Dt.prototype),Le.prototype.constructor=Le,H.prototype=Nn(Dt.prototype),H.prototype.constructor=H,Mn.prototype.clear=function(){this.__data__=ut?ut(null):{},this.size=0},Mn.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},Mn.prototype.get=function(e){var n=this.__data__;if(ut){var t=n[e];return t===l?a:t}return J.call(n,e)?n[e]:a},Mn.prototype.has=function(e){var n=this.__data__;return ut?n[e]!==a:J.call(n,e)},Mn.prototype.set=function(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=ut&&n===a?l:n,this},tn.prototype.clear=function(){this.__data__=[],this.size=0},tn.prototype.delete=function(e){var n=this.__data__,t=$t(n,e);return!(t<0||(t==n.length-1?n.pop():Pt.call(n,t,1),--this.size,0))},tn.prototype.get=function(e){var n=this.__data__,t=$t(n,e);return t<0?a:n[t][1]},tn.prototype.has=function(e){return $t(this.__data__,e)>-1},tn.prototype.set=function(e,n){var t=this.__data__,r=$t(t,e);return r<0?(++this.size,t.push([e,n])):t[r][1]=n,this},rn.prototype.clear=function(){this.size=0,this.__data__={hash:new Mn,map:new(at||tn),string:new Mn}},rn.prototype.delete=function(e){var n=er(this,e).delete(e);return this.size-=n?1:0,n},rn.prototype.get=function(e){return er(this,e).get(e)},rn.prototype.has=function(e){return er(this,e).has(e)},rn.prototype.set=function(e,n){var t=er(this,e),r=t.size;return t.set(e,n),this.size+=t.size==r?0:1,this},In.prototype.add=In.prototype.push=function(e){return this.__data__.set(e,l),this},In.prototype.has=function(e){return this.__data__.has(e)},Ve.prototype.clear=function(){this.__data__=new tn,this.size=0},Ve.prototype.delete=function(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t},Ve.prototype.get=function(e){return this.__data__.get(e)},Ve.prototype.has=function(e){return this.__data__.has(e)},Ve.prototype.set=function(e,n){var t=this.__data__;if(t instanceof tn){var r=t.__data__;if(!at||r.length<199)return r.push([e,n]),this.size=++t.size,this;t=this.__data__=new rn(r)}return t.set(e,n),this.size=t.size,this};var yn=_u(Ye),Gi=_u(Qr,!0);function Qs(e,n){var t=!0;return yn(e,function(r,i,s){return t=!!n(r,i,s)}),t}function Bt(e,n,t){for(var r=-1,i=e.length;++r<i;){var s=e[r],c=n(s);if(c!=null&&(f===a?c==c&&!Oe(c):t(c,f)))var f=c,h=s}return h}function Vi(e,n){var t=[];return yn(e,function(r,i,s){n(r,i,s)&&t.push(r)}),t}function ye(e,n,t,r,i){var s=-1,c=e.length;for(t||(t=fc),i||(i=[]);++s<c;){var f=e[s];n>0&&t(f)?n>1?ye(f,n-1,t,r,i):dn(i,f):r||(i[i.length]=f)}return i}var Xr=mu(),Zi=mu(!0);function Ye(e,n){return e&&Xr(e,n,pe)}function Qr(e,n){return e&&Zi(e,n,pe)}function Nt(e,n){return hn(n,function(t){return cn(e[t])})}function Tn(e,n){for(var t=0,r=(n=_n(n,e)).length;e!=null&&t<r;)e=e[Qe(n[t++])];return t&&t==r?e:a}function Ki(e,n,t){var r=n(e);return D(e)?r:dn(r,t(e))}function we(e){return e==null?e===a?"[object Undefined]":"[object Null]":An&&An in ee(e)?function(n){var t=J.call(n,An),r=n[An];try{n[An]=a;var i=!0}catch{}var s=Et.call(n);return i&&(t?n[An]=r:delete n[An]),s}(e):function(n){return Et.call(n)}(e)}function ea(e,n){return e>n}function ec(e,n){return e!=null&&J.call(e,n)}function nc(e,n){return e!=null&&n in ee(e)}function na(e,n,t){for(var r=t?Ur:At,i=e[0].length,s=e.length,c=s,f=I(s),h=1/0,y=[];c--;){var g=e[c];c&&n&&(g=te(g,je(n))),h=ve(g.length,h),f[c]=!t&&(n||i>=120&&g.length>=120)?new In(c&&g):a}g=e[0];var S=-1,C=f[0];e:for(;++S<i&&y.length<h;){var k=g[S],j=n?n(k):k;if(k=t||k!==0?k:0,!(C?tt(C,j):r(y,j,t))){for(c=s;--c;){var v=f[c];if(!(v?tt(v,j):r(e[c],j,t)))continue e}C&&C.push(j),y.push(k)}}return y}function lt(e,n,t){var r=(e=Fu(e,n=_n(n,e)))==null?e:e[Qe(De(n))];return r==null?a:Ee(r,e,t)}function Ji(e){return ue(e)&&we(e)==ie}function ft(e,n,t,r,i){return e===n||(e==null||n==null||!ue(e)&&!ue(n)?e!=e&&n!=n:function(s,c,f,h,y,g){var S=D(s),C=D(c),k=S?Re:_e(s),j=C?Re:_e(c),v=(k=k==ie?nn:k)==nn,R=(j=j==ie?nn:j)==nn,O=k==j;if(O&&wn(s)){if(!wn(c))return!1;S=!0,v=!1}if(O&&!v)return g||(g=new Ve),S||Zn(s)?Tu(s,c,f,h,y,g):function(E,P,de,le,Se,ne,me){switch(de){case Pn:if(E.byteLength!=P.byteLength||E.byteOffset!=P.byteOffset)return!1;E=E.buffer,P=P.buffer;case nt:return!(E.byteLength!=P.byteLength||!ne(new qt(E),new qt(P)));case Y:case Be:case Yn:return Ze(+E,+P);case fn:return E.name==P.name&&E.message==P.message;case Xn:case Qn:return E==P+"";case Ne:var en=Hr;case He:var bn=1&le;if(en||(en=It),E.size!=P.size&&!bn)return!1;var lr=me.get(E);if(lr)return lr==P;le|=2,me.set(E,P);var Da=Tu(en(E),en(P),le,Se,ne,me);return me.delete(E),Da;case bt:if(ot)return ot.call(E)==ot.call(P)}return!1}(s,c,k,f,h,y,g);if(!(1&f)){var M=v&&J.call(s,"__wrapped__"),W=R&&J.call(c,"__wrapped__");if(M||W){var L=M?s.value():s,Z=W?c.value():c;return g||(g=new Ve),y(L,Z,f,h,g)}}return!!O&&(g||(g=new Ve),function(E,P,de,le,Se,ne){var me=1&de,en=ya(E),bn=en.length,lr=ya(P),Da=lr.length;if(bn!=Da&&!me)return!1;for(var fr=bn;fr--;){var qn=en[fr];if(!(me?qn in P:J.call(P,qn)))return!1}var wo=ne.get(E),bo=ne.get(P);if(wo&&bo)return wo==P&&bo==E;var hr=!0;ne.set(E,P),ne.set(P,E);for(var $a=me;++fr<bn;){var dr=E[qn=en[fr]],pr=P[qn];if(le)var So=me?le(pr,dr,qn,P,E,ne):le(dr,pr,qn,E,P,ne);if(!(So===a?dr===pr||Se(dr,pr,de,le,ne):So)){hr=!1;break}$a||($a=qn=="constructor")}if(hr&&!$a){var gr=E.constructor,yr=P.constructor;gr==yr||!("constructor"in E)||!("constructor"in P)||typeof gr=="function"&&gr instanceof gr&&typeof yr=="function"&&yr instanceof yr||(hr=!1)}return ne.delete(E),ne.delete(P),hr}(s,c,f,h,y,g))}(e,n,t,r,ft,i))}function ta(e,n,t,r){var i=t.length,s=i,c=!r;if(e==null)return!s;for(e=ee(e);i--;){var f=t[i];if(c&&f[2]?f[1]!==e[f[0]]:!(f[0]in e))return!1}for(;++i<s;){var h=(f=t[i])[0],y=e[h],g=f[1];if(c&&f[2]){if(y===a&&!(h in e))return!1}else{var S=new Ve;if(r)var C=r(y,g,h,e,n,S);if(!(C===a?ft(g,y,3,r,S):C))return!1}}return!0}function Yi(e){return!(!ae(e)||(n=e,qi&&qi in n))&&(cn(e)?Ps:Xo).test(En(e));var n}function Xi(e){return typeof e=="function"?e:e==null?Ce:typeof e=="object"?D(e)?nu(e[0],e[1]):eu(e):mo(e)}function ra(e){if(!pt(e))return Ds(e);var n=[];for(var t in ee(e))J.call(e,t)&&t!="constructor"&&n.push(t);return n}function tc(e){if(!ae(e))return function(i){var s=[];if(i!=null)for(var c in ee(i))s.push(c);return s}(e);var n=pt(e),t=[];for(var r in e)(r!="constructor"||!n&&J.call(e,r))&&t.push(r);return t}function aa(e,n){return e<n}function Qi(e,n){var t=-1,r=Me(e)?I(e.length):[];return yn(e,function(i,s,c){r[++t]=n(i,s,c)}),r}function eu(e){var n=ma(e);return n.length==1&&n[0][2]?Ou(n[0][0],n[0][1]):function(t){return t===e||ta(t,e,n)}}function nu(e,n){return ba(e)&&qu(n)?Ou(Qe(e),n):function(t){var r=Ea(t,e);return r===a&&r===n?ja(t,e):ft(n,r,3)}}function Ht(e,n,t,r,i){e!==n&&Xr(n,function(s,c){if(i||(i=new Ve),ae(s))(function(h,y,g,S,C,k,j){var v=xa(h,g),R=xa(y,g),O=j.get(R);if(O)Jr(h,g,O);else{var M=k?k(v,R,g+"",h,y,j):a,W=M===a;if(W){var L=D(R),Z=!L&&wn(R),E=!L&&!Z&&Zn(R);M=R,L||Z||E?D(v)?M=v:ce(v)?M=Ae(v):Z?(W=!1,M=du(R,!0)):E?(W=!1,M=pu(R,!0)):M=[]:yt(R)||jn(R)?(M=v,jn(v)?M=so(v):ae(v)&&!cn(v)||(M=ju(R))):W=!1}W&&(j.set(R,M),C(M,R,S,k,j),j.delete(R)),Jr(h,g,M)}})(e,n,c,t,Ht,r,i);else{var f=r?r(xa(e,c),s,c+"",e,n,i):a;f===a&&(f=s),Jr(e,c,f)}},Ie)}function tu(e,n){var t=e.length;if(t)return sn(n+=n<0?t:0,t)?e[n]:a}function ru(e,n,t){n=n.length?te(n,function(s){return D(s)?function(c){return Tn(c,s.length===1?s[0]:s)}:s}):[Ce];var r=-1;n=te(n,je(q()));var i=Qi(e,function(s,c,f){var h=te(n,function(y){return y(s)});return{criteria:h,index:++r,value:s}});return function(s,c){var f=s.length;for(s.sort(c);f--;)s[f]=s[f].value;return s}(i,function(s,c){return function(f,h,y){for(var g=-1,S=f.criteria,C=h.criteria,k=S.length,j=y.length;++g<k;){var v=gu(S[g],C[g]);if(v)return g>=j?v:v*(y[g]=="desc"?-1:1)}return f.index-h.index}(s,c,t)})}function au(e,n,t){for(var r=-1,i=n.length,s={};++r<i;){var c=n[r],f=Tn(e,c);t(f,c)&&ht(s,_n(c,e),f)}return s}function ia(e,n,t,r){var i=r?Rs:Un,s=-1,c=n.length,f=e;for(e===n&&(n=Ae(n)),t&&(f=te(e,je(t)));++s<c;)for(var h=0,y=n[s],g=t?t(y):y;(h=i(f,g,h,r))>-1;)f!==e&&Pt.call(f,h,1),Pt.call(e,h,1);return e}function iu(e,n){for(var t=e?n.length:0,r=t-1;t--;){var i=n[t];if(t==r||i!==s){var s=i;sn(i)?Pt.call(e,i,1):ca(e,i)}}return e}function ua(e,n){return e+Lt(zi()*(n-e+1))}function oa(e,n){var t="";if(!e||n<1||n>oe)return t;do n%2&&(t+=e),(n=Lt(n/2))&&(e+=e);while(n);return t}function B(e,n){return Ra(Pu(e,n,Ce),e+"")}function rc(e){return $i(Kn(e))}function ac(e,n){var t=Kn(e);return nr(t,Cn(n,0,t.length))}function ht(e,n,t,r){if(!ae(e))return e;for(var i=-1,s=(n=_n(n,e)).length,c=s-1,f=e;f!=null&&++i<s;){var h=Qe(n[i]),y=t;if(h==="__proto__"||h==="constructor"||h==="prototype")return e;if(i!=c){var g=f[h];(y=r?r(g,h,f):a)===a&&(y=ae(g)?g:sn(n[i+1])?[]:{})}st(f,h,y),f=f[h]}return e}var uu=zt?function(e,n){return zt.set(e,n),e}:Ce,ic=Ft?function(e,n){return Ft(e,"toString",{configurable:!0,enumerable:!1,value:Oa(n),writable:!0})}:Ce;function uc(e){return nr(Kn(e))}function We(e,n,t){var r=-1,i=e.length;n<0&&(n=-n>i?0:i+n),(t=t>i?i:t)<0&&(t+=i),i=n>t?0:t-n>>>0,n>>>=0;for(var s=I(i);++r<i;)s[r]=e[r+n];return s}function oc(e,n){var t;return yn(e,function(r,i,s){return!(t=n(r,i,s))}),!!t}function Gt(e,n,t){var r=0,i=e==null?r:e.length;if(typeof n=="number"&&n==n&&i<=2147483647){for(;r<i;){var s=r+i>>>1,c=e[s];c!==null&&!Oe(c)&&(t?c<=n:c<n)?r=s+1:i=s}return i}return sa(e,n,Ce,t)}function sa(e,n,t,r){var i=0,s=e==null?0:e.length;if(s===0)return 0;for(var c=(n=t(n))!=n,f=n===null,h=Oe(n),y=n===a;i<s;){var g=Lt((i+s)/2),S=t(e[g]),C=S!==a,k=S===null,j=S==S,v=Oe(S);if(c)var R=r||j;else R=y?j&&(r||C):f?j&&C&&(r||!k):h?j&&C&&!k&&(r||!v):!k&&!v&&(r?S<=n:S<n);R?i=g+1:s=g}return ve(s,4294967294)}function ou(e,n){for(var t=-1,r=e.length,i=0,s=[];++t<r;){var c=e[t],f=n?n(c):c;if(!t||!Ze(f,h)){var h=f;s[i++]=c===0?0:c}}return s}function su(e){return typeof e=="number"?e:Oe(e)?U:+e}function qe(e){if(typeof e=="string")return e;if(D(e))return te(e,qe)+"";if(Oe(e))return Wi?Wi.call(e):"";var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function vn(e,n,t){var r=-1,i=At,s=e.length,c=!0,f=[],h=f;if(t)c=!1,i=Ur;else if(s>=200){var y=n?null:cc(e);if(y)return It(y);c=!1,i=tt,h=new In}else h=n?[]:f;e:for(;++r<s;){var g=e[r],S=n?n(g):g;if(g=t||g!==0?g:0,c&&S==S){for(var C=h.length;C--;)if(h[C]===S)continue e;n&&h.push(S),f.push(g)}else i(h,S,t)||(h!==f&&h.push(S),f.push(g))}return f}function ca(e,n){return(e=Fu(e,n=_n(n,e)))==null||delete e[Qe(De(n))]}function cu(e,n,t,r){return ht(e,n,t(Tn(e,n)),r)}function Vt(e,n,t,r){for(var i=e.length,s=r?i:-1;(r?s--:++s<i)&&n(e[s],s,e););return t?We(e,r?0:s,r?s+1:i):We(e,r?s+1:0,r?i:s)}function lu(e,n){var t=e;return t instanceof H&&(t=t.value()),Lr(n,function(r,i){return i.func.apply(i.thisArg,dn([r],i.args))},t)}function la(e,n,t){var r=e.length;if(r<2)return r?vn(e[0]):[];for(var i=-1,s=I(r);++i<r;)for(var c=e[i],f=-1;++f<r;)f!=i&&(s[i]=ct(s[i]||c,e[f],n,t));return vn(ye(s,1),n,t)}function fu(e,n,t){for(var r=-1,i=e.length,s=n.length,c={};++r<i;){var f=r<s?n[r]:a;t(c,e[r],f)}return c}function fa(e){return ce(e)?e:[]}function ha(e){return typeof e=="function"?e:Ce}function _n(e,n){return D(e)?e:ba(e,n)?[e]:Wu(K(e))}var sc=B;function mn(e,n,t){var r=e.length;return t=t===a?r:t,!n&&t>=r?e:We(e,n,t)}var hu=Fs||function(e){return ge.clearTimeout(e)};function du(e,n){if(n)return e.slice();var t=e.length,r=Oi?Oi(t):new e.constructor(t);return e.copy(r),r}function da(e){var n=new e.constructor(e.byteLength);return new qt(n).set(new qt(e)),n}function pu(e,n){var t=n?da(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}function gu(e,n){if(e!==n){var t=e!==a,r=e===null,i=e==e,s=Oe(e),c=n!==a,f=n===null,h=n==n,y=Oe(n);if(!f&&!y&&!s&&e>n||s&&c&&h&&!f&&!y||r&&c&&h||!t&&h||!i)return 1;if(!r&&!s&&!y&&e<n||y&&t&&i&&!r&&!s||f&&t&&i||!c&&i||!h)return-1}return 0}function yu(e,n,t,r){for(var i=-1,s=e.length,c=t.length,f=-1,h=n.length,y=he(s-c,0),g=I(h+y),S=!r;++f<h;)g[f]=n[f];for(;++i<c;)(S||i<s)&&(g[t[i]]=e[i]);for(;y--;)g[f++]=e[i++];return g}function vu(e,n,t,r){for(var i=-1,s=e.length,c=-1,f=t.length,h=-1,y=n.length,g=he(s-f,0),S=I(g+y),C=!r;++i<g;)S[i]=e[i];for(var k=i;++h<y;)S[k+h]=n[h];for(;++c<f;)(C||i<s)&&(S[k+t[c]]=e[i++]);return S}function Ae(e,n){var t=-1,r=e.length;for(n||(n=I(r));++t<r;)n[t]=e[t];return n}function Xe(e,n,t,r){var i=!t;t||(t={});for(var s=-1,c=n.length;++s<c;){var f=n[s],h=r?r(t[f],e[f],f,t,e):a;h===a&&(h=e[f]),i?an(t,f,h):st(t,f,h)}return t}function Zt(e,n){return function(t,r){var i=D(t)?ws:Xs,s=n?n():{};return i(t,e,q(r,2),s)}}function Hn(e){return B(function(n,t){var r=-1,i=t.length,s=i>1?t[i-1]:a,c=i>2?t[2]:a;for(s=e.length>3&&typeof s=="function"?(i--,s):a,c&&be(t[0],t[1],c)&&(s=i<3?a:s,i=1),n=ee(n);++r<i;){var f=t[r];f&&e(n,f,r,s)}return n})}function _u(e,n){return function(t,r){if(t==null)return t;if(!Me(t))return e(t,r);for(var i=t.length,s=n?i:-1,c=ee(t);(n?s--:++s<i)&&r(c[s],s,c)!==!1;);return t}}function mu(e){return function(n,t,r){for(var i=-1,s=ee(n),c=r(n),f=c.length;f--;){var h=c[e?f:++i];if(t(s[h],h,s)===!1)break}return n}}function wu(e){return function(n){var t=Ln(n=K(n))?Ge(n):a,r=t?t[0]:n.charAt(0),i=t?mn(t,1).join(""):n.slice(1);return r[e]()+i}}function Gn(e){return function(n){return Lr(vo(yo(n).replace(cs,"")),e,"")}}function dt(e){return function(){var n=arguments;switch(n.length){case 0:return new e;case 1:return new e(n[0]);case 2:return new e(n[0],n[1]);case 3:return new e(n[0],n[1],n[2]);case 4:return new e(n[0],n[1],n[2],n[3]);case 5:return new e(n[0],n[1],n[2],n[3],n[4]);case 6:return new e(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new e(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var t=Nn(e.prototype),r=e.apply(t,n);return ae(r)?r:t}}function bu(e){return function(n,t,r){var i=ee(n);if(!Me(n)){var s=q(t,3);n=pe(n),t=function(f){return s(i[f],f,i)}}var c=e(n,t,r);return c>-1?i[s?n[c]:c]:a}}function Su(e){return on(function(n){var t=n.length,r=t,i=Le.prototype.thru;for(e&&n.reverse();r--;){var s=n[r];if(typeof s!="function")throw new Ue(o);if(i&&!c&&Qt(s)=="wrapper")var c=new Le([],!0)}for(r=c?r:t;++r<t;){var f=Qt(s=n[r]),h=f=="wrapper"?_a(s):a;c=h&&Sa(h[0])&&h[1]==424&&!h[4].length&&h[9]==1?c[Qt(h[0])].apply(c,h[3]):s.length==1&&Sa(s)?c[f]():c.thru(s)}return function(){var y=arguments,g=y[0];if(c&&y.length==1&&D(g))return c.plant(g).value();for(var S=0,C=t?n[S].apply(this,y):g;++S<t;)C=n[S].call(this,C);return C}})}function Kt(e,n,t,r,i,s,c,f,h,y){var g=n&F,S=1&n,C=2&n,k=24&n,j=512&n,v=C?a:dt(e);return function R(){for(var O=arguments.length,M=I(O),W=O;W--;)M[W]=arguments[W];if(k)var L=Vn(R),Z=function(le,Se){for(var ne=le.length,me=0;ne--;)le[ne]===Se&&++me;return me}(M,L);if(r&&(M=yu(M,r,i,k)),s&&(M=vu(M,s,c,k)),O-=Z,k&&O<y){var E=pn(M,L);return Au(e,n,Kt,R.placeholder,t,M,E,f,h,y-O)}var P=S?t:this,de=C?P[e]:e;return O=M.length,f?M=function(le,Se){for(var ne=le.length,me=ve(Se.length,ne),en=Ae(le);me--;){var bn=Se[me];le[me]=sn(bn,ne)?en[bn]:a}return le}(M,f):j&&O>1&&M.reverse(),g&&h<O&&(M.length=h),this&&this!==ge&&this instanceof R&&(de=v||dt(de)),de.apply(P,M)}}function xu(e,n){return function(t,r){return function(i,s,c,f){return Ye(i,function(h,y,g){s(f,c(h),y,g)}),f}(t,e,n(r),{})}}function Jt(e,n){return function(t,r){var i;if(t===a&&r===a)return n;if(t!==a&&(i=t),r!==a){if(i===a)return r;typeof t=="string"||typeof r=="string"?(t=qe(t),r=qe(r)):(t=su(t),r=su(r)),i=e(t,r)}return i}}function pa(e){return on(function(n){return n=te(n,je(q())),B(function(t){var r=this;return e(n,function(i){return Ee(i,r,t)})})})}function Yt(e,n){var t=(n=n===a?" ":qe(n)).length;if(t<2)return t?oa(n,e):n;var r=oa(n,Ut(e/zn(n)));return Ln(n)?mn(Ge(r),0,e).join(""):r.slice(0,e)}function Ru(e){return function(n,t,r){return r&&typeof r!="number"&&be(n,t,r)&&(t=r=a),n=ln(n),t===a?(t=n,n=0):t=ln(t),function(i,s,c,f){for(var h=-1,y=he(Ut((s-i)/(c||1)),0),g=I(y);y--;)g[f?y:++h]=i,i+=c;return g}(n,t,r=r===a?n<t?1:-1:ln(r),e)}}function Xt(e){return function(n,t){return typeof n=="string"&&typeof t=="string"||(n=$e(n),t=$e(t)),e(n,t)}}function Au(e,n,t,r,i,s,c,f,h,y){var g=8&n;n|=g?x:w,4&(n&=~(g?w:x))||(n&=-4);var S=[e,n,i,g?s:a,g?c:a,g?a:s,g?a:c,f,h,y],C=t.apply(a,S);return Sa(e)&&Uu(C,S),C.placeholder=r,Lu(C,e,n)}function ga(e){var n=Je[e];return function(t,r){if(t=$e(t),(r=r==null?0:ve($(r),292))&&Li(t)){var i=(K(t)+"e").split("e");return+((i=(K(n(i[0]+"e"+(+i[1]+r)))+"e").split("e"))[0]+"e"+(+i[1]-r))}return n(t)}}var cc=$n&&1/It(new $n([,-0]))[1]==re?function(e){return new $n(e)}:Ua;function Mu(e){return function(n){var t=_e(n);return t==Ne?Hr(n):t==He?Cs(n):function(r,i){return te(i,function(s){return[s,r[s]]})}(n,e(n))}}function un(e,n,t,r,i,s,c,f){var h=2&n;if(!h&&typeof e!="function")throw new Ue(o);var y=r?r.length:0;if(y||(n&=-97,r=i=a),c=c===a?c:he($(c),0),f=f===a?f:$(f),y-=i?i.length:0,n&w){var g=r,S=i;r=i=a}var C=h?a:_a(e),k=[e,n,t,r,i,g,S,s,c,f];if(C&&function(v,R){var O=v[1],M=R[1],W=O|M,L=W<131,Z=M==F&&O==8||M==F&&O==G&&v[7].length<=R[8]||M==384&&R[7].length<=R[8]&&O==8;if(!L&&!Z)return v;1&M&&(v[2]=R[2],W|=1&O?0:4);var E=R[3];if(E){var P=v[3];v[3]=P?yu(P,E,R[4]):E,v[4]=P?pn(v[3],d):R[4]}(E=R[5])&&(P=v[5],v[5]=P?vu(P,E,R[6]):E,v[6]=P?pn(v[5],d):R[6]),(E=R[7])&&(v[7]=E),M&F&&(v[8]=v[8]==null?R[8]:ve(v[8],R[8])),v[9]==null&&(v[9]=R[9]),v[0]=R[0],v[1]=W}(k,C),e=k[0],n=k[1],t=k[2],r=k[3],i=k[4],!(f=k[9]=k[9]===a?h?0:e.length:he(k[9]-y,0))&&24&n&&(n&=-25),n&&n!=1)j=n==8||n==b?function(v,R,O){var M=dt(v);return function W(){for(var L=arguments.length,Z=I(L),E=L,P=Vn(W);E--;)Z[E]=arguments[E];var de=L<3&&Z[0]!==P&&Z[L-1]!==P?[]:pn(Z,P);return(L-=de.length)<O?Au(v,R,Kt,W.placeholder,a,Z,de,a,a,O-L):Ee(this&&this!==ge&&this instanceof W?M:v,this,Z)}}(e,n,f):n!=x&&n!=33||i.length?Kt.apply(a,k):function(v,R,O,M){var W=1&R,L=dt(v);return function Z(){for(var E=-1,P=arguments.length,de=-1,le=M.length,Se=I(le+P),ne=this&&this!==ge&&this instanceof Z?L:v;++de<le;)Se[de]=M[de];for(;P--;)Se[de++]=arguments[++E];return Ee(ne,W?O:this,Se)}}(e,n,t,r);else var j=function(v,R,O){var M=1&R,W=dt(v);return function L(){return(this&&this!==ge&&this instanceof L?W:v).apply(M?O:this,arguments)}}(e,n,t);return Lu((C?uu:Uu)(j,k),e,n)}function Iu(e,n,t,r){return e===a||Ze(e,Dn[t])&&!J.call(r,t)?n:e}function Cu(e,n,t,r,i,s){return ae(e)&&ae(n)&&(s.set(n,e),Ht(e,n,a,Cu,s),s.delete(n)),e}function lc(e){return yt(e)?a:e}function Tu(e,n,t,r,i,s){var c=1&t,f=e.length,h=n.length;if(f!=h&&!(c&&h>f))return!1;var y=s.get(e),g=s.get(n);if(y&&g)return y==n&&g==e;var S=-1,C=!0,k=2&t?new In:a;for(s.set(e,n),s.set(n,e);++S<f;){var j=e[S],v=n[S];if(r)var R=c?r(v,j,S,n,e,s):r(j,v,S,e,n,s);if(R!==a){if(R)continue;C=!1;break}if(k){if(!zr(n,function(O,M){if(!tt(k,M)&&(j===O||i(j,O,t,r,s)))return k.push(M)})){C=!1;break}}else if(j!==v&&!i(j,v,t,r,s)){C=!1;break}}return s.delete(e),s.delete(n),C}function on(e){return Ra(Pu(e,a,Nu),e+"")}function ya(e){return Ki(e,pe,wa)}function va(e){return Ki(e,Ie,ku)}var _a=zt?function(e){return zt.get(e)}:Ua;function Qt(e){for(var n=e.name+"",t=Bn[n],r=J.call(Bn,n)?t.length:0;r--;){var i=t[r],s=i.func;if(s==null||s==e)return i.name}return n}function Vn(e){return(J.call(u,"placeholder")?u:e).placeholder}function q(){var e=u.iteratee||Pa;return e=e===Pa?Xi:e,arguments.length?e(arguments[0],arguments[1]):e}function er(e,n){var t,r,i=e.__data__;return((r=typeof(t=n))=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null)?i[typeof n=="string"?"string":"hash"]:i.map}function ma(e){for(var n=pe(e),t=n.length;t--;){var r=n[t],i=e[r];n[t]=[r,i,qu(i)]}return n}function kn(e,n){var t=function(r,i){return r==null?a:r[i]}(e,n);return Yi(t)?t:a}var wa=Vr?function(e){return e==null?[]:(e=ee(e),hn(Vr(e),function(n){return Fi.call(e,n)}))}:La,ku=Vr?function(e){for(var n=[];e;)dn(n,wa(e)),e=Ot(e);return n}:La,_e=we;function Eu(e,n,t){for(var r=-1,i=(n=_n(n,e)).length,s=!1;++r<i;){var c=Qe(n[r]);if(!(s=e!=null&&t(e,c)))break;e=e[c]}return s||++r!=i?s:!!(i=e==null?0:e.length)&&or(i)&&sn(c,i)&&(D(e)||jn(e))}function ju(e){return typeof e.constructor!="function"||pt(e)?{}:Nn(Ot(e))}function fc(e){return D(e)||jn(e)||!!(Ui&&e&&e[Ui])}function sn(e,n){var t=typeof e;return!!(n=n??oe)&&(t=="number"||t!="symbol"&&es.test(e))&&e>-1&&e%1==0&&e<n}function be(e,n,t){if(!ae(t))return!1;var r=typeof n;return!!(r=="number"?Me(t)&&sn(n,t.length):r=="string"&&n in t)&&Ze(t[n],e)}function ba(e,n){if(D(e))return!1;var t=typeof e;return!(t!="number"&&t!="symbol"&&t!="boolean"&&e!=null&&!Oe(e))||zo.test(e)||!Lo.test(e)||n!=null&&e in ee(n)}function Sa(e){var n=Qt(e),t=u[n];if(typeof t!="function"||!(n in H.prototype))return!1;if(e===t)return!0;var r=_a(t);return!!r&&e===r[0]}(Zr&&_e(new Zr(new ArrayBuffer(1)))!=Pn||at&&_e(new at)!=Ne||Kr&&_e(Kr.resolve())!=Ga||$n&&_e(new $n)!=He||it&&_e(new it)!=et)&&(_e=function(e){var n=we(e),t=n==nn?e.constructor:a,r=t?En(t):"";if(r)switch(r){case Hs:return Pn;case Gs:return Ne;case Vs:return Ga;case Zs:return He;case Ks:return et}return n});var hc=Tt?cn:za;function pt(e){var n=e&&e.constructor;return e===(typeof n=="function"&&n.prototype||Dn)}function qu(e){return e==e&&!ae(e)}function Ou(e,n){return function(t){return t!=null&&t[e]===n&&(n!==a||e in ee(t))}}function Pu(e,n,t){return n=he(n===a?e.length-1:n,0),function(){for(var r=arguments,i=-1,s=he(r.length-n,0),c=I(s);++i<s;)c[i]=r[n+i];i=-1;for(var f=I(n+1);++i<n;)f[i]=r[i];return f[n]=t(c),Ee(e,this,f)}}function Fu(e,n){return n.length<2?e:Tn(e,We(n,0,-1))}function xa(e,n){if((n!=="constructor"||typeof e[n]!="function")&&n!="__proto__")return e[n]}var Uu=zu(uu),gt=Ls||function(e,n){return ge.setTimeout(e,n)},Ra=zu(ic);function Lu(e,n,t){var r=n+"";return Ra(e,function(i,s){var c=s.length;if(!c)return i;var f=c-1;return s[f]=(c>1?"& ":"")+s[f],s=s.join(c>2?", ":" "),i.replace(Bo,`{
/* [wrapped with `+s+`] */
`)}(r,function(i,s){return Fe(se,function(c){var f="_."+c[0];s&c[1]&&!At(i,f)&&i.push(f)}),i.sort()}(function(i){var s=i.match(No);return s?s[1].split(Ho):[]}(r),t)))}function zu(e){var n=0,t=0;return function(){var r=$s(),i=16-(r-t);if(t=r,i>0){if(++n>=800)return arguments[0]}else n=0;return e.apply(a,arguments)}}function nr(e,n){var t=-1,r=e.length,i=r-1;for(n=n===a?r:n;++t<n;){var s=ua(t,i),c=e[s];e[s]=e[t],e[t]=c}return e.length=n,e}var Wu=function(e){var n=ir(e,function(r){return t.size===500&&t.clear(),r}),t=n.cache;return n}(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(Wo,function(t,r,i,s){n.push(i?s.replace(Zo,"$1"):r||t)}),n});function Qe(e){if(typeof e=="string"||Oe(e))return e;var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function En(e){if(e!=null){try{return kt.call(e)}catch{}try{return e+""}catch{}}return""}function Du(e){if(e instanceof H)return e.clone();var n=new Le(e.__wrapped__,e.__chain__);return n.__actions__=Ae(e.__actions__),n.__index__=e.__index__,n.__values__=e.__values__,n}var dc=B(function(e,n){return ce(e)?ct(e,ye(n,1,ce,!0)):[]}),pc=B(function(e,n){var t=De(n);return ce(t)&&(t=a),ce(e)?ct(e,ye(n,1,ce,!0),q(t,2)):[]}),gc=B(function(e,n){var t=De(n);return ce(t)&&(t=a),ce(e)?ct(e,ye(n,1,ce,!0),a,t):[]});function $u(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:$(t);return i<0&&(i=he(r+i,0)),Mt(e,q(n,3),i)}function Bu(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r-1;return t!==a&&(i=$(t),i=t<0?he(r+i,0):ve(i,r-1)),Mt(e,q(n,3),i,!0)}function Nu(e){return e!=null&&e.length?ye(e,1):[]}function Hu(e){return e&&e.length?e[0]:a}var yc=B(function(e){var n=te(e,fa);return n.length&&n[0]===e[0]?na(n):[]}),vc=B(function(e){var n=De(e),t=te(e,fa);return n===De(t)?n=a:t.pop(),t.length&&t[0]===e[0]?na(t,q(n,2)):[]}),_c=B(function(e){var n=De(e),t=te(e,fa);return(n=typeof n=="function"?n:a)&&t.pop(),t.length&&t[0]===e[0]?na(t,a,n):[]});function De(e){var n=e==null?0:e.length;return n?e[n-1]:a}var mc=B(Gu);function Gu(e,n){return e&&e.length&&n&&n.length?ia(e,n):e}var wc=on(function(e,n){var t=e==null?0:e.length,r=Yr(e,n);return iu(e,te(n,function(i){return sn(i,t)?+i:i}).sort(gu)),r});function Aa(e){return e==null?e:Ns.call(e)}var bc=B(function(e){return vn(ye(e,1,ce,!0))}),Sc=B(function(e){var n=De(e);return ce(n)&&(n=a),vn(ye(e,1,ce,!0),q(n,2))}),xc=B(function(e){var n=De(e);return n=typeof n=="function"?n:a,vn(ye(e,1,ce,!0),a,n)});function Ma(e){if(!e||!e.length)return[];var n=0;return e=hn(e,function(t){if(ce(t))return n=he(t.length,n),!0}),Br(n,function(t){return te(e,Wr(t))})}function Vu(e,n){if(!e||!e.length)return[];var t=Ma(e);return n==null?t:te(t,function(r){return Ee(n,a,r)})}var Rc=B(function(e,n){return ce(e)?ct(e,n):[]}),Ac=B(function(e){return la(hn(e,ce))}),Mc=B(function(e){var n=De(e);return ce(n)&&(n=a),la(hn(e,ce),q(n,2))}),Ic=B(function(e){var n=De(e);return n=typeof n=="function"?n:a,la(hn(e,ce),a,n)}),Cc=B(Ma),Tc=B(function(e){var n=e.length,t=n>1?e[n-1]:a;return t=typeof t=="function"?(e.pop(),t):a,Vu(e,t)});function Zu(e){var n=u(e);return n.__chain__=!0,n}function tr(e,n){return n(e)}var kc=on(function(e){var n=e.length,t=n?e[0]:0,r=this.__wrapped__,i=function(s){return Yr(s,e)};return!(n>1||this.__actions__.length)&&r instanceof H&&sn(t)?((r=r.slice(t,+t+(n?1:0))).__actions__.push({func:tr,args:[i],thisArg:a}),new Le(r,this.__chain__).thru(function(s){return n&&!s.length&&s.push(a),s})):this.thru(i)}),Ec=Zt(function(e,n,t){J.call(e,t)?++e[t]:an(e,t,1)}),jc=bu($u),qc=bu(Bu);function Ku(e,n){return(D(e)?Fe:yn)(e,q(n,3))}function Ju(e,n){return(D(e)?bs:Gi)(e,q(n,3))}var Oc=Zt(function(e,n,t){J.call(e,t)?e[t].push(n):an(e,t,[n])}),Pc=B(function(e,n,t){var r=-1,i=typeof n=="function",s=Me(e)?I(e.length):[];return yn(e,function(c){s[++r]=i?Ee(n,c,t):lt(c,n,t)}),s}),Fc=Zt(function(e,n,t){an(e,t,n)});function rr(e,n){return(D(e)?te:Qi)(e,q(n,3))}var Uc=Zt(function(e,n,t){e[t?0:1].push(n)},function(){return[[],[]]}),Lc=B(function(e,n){if(e==null)return[];var t=n.length;return t>1&&be(e,n[0],n[1])?n=[]:t>2&&be(n[0],n[1],n[2])&&(n=[n[0]]),ru(e,ye(n,1),[])}),ar=Us||function(){return ge.Date.now()};function Yu(e,n,t){return n=t?a:n,n=e&&n==null?e.length:n,un(e,F,a,a,a,a,n)}function Xu(e,n){var t;if(typeof n!="function")throw new Ue(o);return e=$(e),function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=a),t}}var Ia=B(function(e,n,t){var r=1;if(t.length){var i=pn(t,Vn(Ia));r|=x}return un(e,r,n,t,i)}),Qu=B(function(e,n,t){var r=3;if(t.length){var i=pn(t,Vn(Qu));r|=x}return un(n,r,e,t,i)});function eo(e,n,t){var r,i,s,c,f,h,y=0,g=!1,S=!1,C=!0;if(typeof e!="function")throw new Ue(o);function k(M){var W=r,L=i;return r=i=a,y=M,c=e.apply(L,W)}function j(M){var W=M-h;return h===a||W>=n||W<0||S&&M-y>=s}function v(){var M=ar();if(j(M))return R(M);f=gt(v,function(W){var L=n-(W-h);return S?ve(L,s-(W-y)):L}(M))}function R(M){return f=a,C&&r?k(M):(r=i=a,c)}function O(){var M=ar(),W=j(M);if(r=arguments,i=this,h=M,W){if(f===a)return function(L){return y=L,f=gt(v,n),g?k(L):c}(h);if(S)return hu(f),f=gt(v,n),k(h)}return f===a&&(f=gt(v,n)),c}return n=$e(n)||0,ae(t)&&(g=!!t.leading,s=(S="maxWait"in t)?he($e(t.maxWait)||0,n):s,C="trailing"in t?!!t.trailing:C),O.cancel=function(){f!==a&&hu(f),y=0,r=h=i=f=a},O.flush=function(){return f===a?c:R(ar())},O}var zc=B(function(e,n){return Hi(e,1,n)}),Wc=B(function(e,n,t){return Hi(e,$e(n)||0,t)});function ir(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new Ue(o);var t=function(){var r=arguments,i=n?n.apply(this,r):r[0],s=t.cache;if(s.has(i))return s.get(i);var c=e.apply(this,r);return t.cache=s.set(i,c)||s,c};return t.cache=new(ir.Cache||rn),t}function ur(e){if(typeof e!="function")throw new Ue(o);return function(){var n=arguments;switch(n.length){case 0:return!e.call(this);case 1:return!e.call(this,n[0]);case 2:return!e.call(this,n[0],n[1]);case 3:return!e.call(this,n[0],n[1],n[2])}return!e.apply(this,n)}}ir.Cache=rn;var Dc=sc(function(e,n){var t=(n=n.length==1&&D(n[0])?te(n[0],je(q())):te(ye(n,1),je(q()))).length;return B(function(r){for(var i=-1,s=ve(r.length,t);++i<s;)r[i]=n[i].call(this,r[i]);return Ee(e,this,r)})}),Ca=B(function(e,n){var t=pn(n,Vn(Ca));return un(e,x,a,n,t)}),no=B(function(e,n){var t=pn(n,Vn(no));return un(e,w,a,n,t)}),$c=on(function(e,n){return un(e,G,a,a,a,n)});function Ze(e,n){return e===n||e!=e&&n!=n}var Bc=Xt(ea),Nc=Xt(function(e,n){return e>=n}),jn=Ji(function(){return arguments}())?Ji:function(e){return ue(e)&&J.call(e,"callee")&&!Fi.call(e,"callee")},D=I.isArray,Hc=vi?je(vi):function(e){return ue(e)&&we(e)==nt};function Me(e){return e!=null&&or(e.length)&&!cn(e)}function ce(e){return ue(e)&&Me(e)}var wn=zs||za,Gc=_i?je(_i):function(e){return ue(e)&&we(e)==Be};function Ta(e){if(!ue(e))return!1;var n=we(e);return n==fn||n=="[object DOMException]"||typeof e.message=="string"&&typeof e.name=="string"&&!yt(e)}function cn(e){if(!ae(e))return!1;var n=we(e);return n==On||n==Ha||n=="[object AsyncFunction]"||n=="[object Proxy]"}function to(e){return typeof e=="number"&&e==$(e)}function or(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=oe}function ae(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function ue(e){return e!=null&&typeof e=="object"}var ro=mi?je(mi):function(e){return ue(e)&&_e(e)==Ne};function ao(e){return typeof e=="number"||ue(e)&&we(e)==Yn}function yt(e){if(!ue(e)||we(e)!=nn)return!1;var n=Ot(e);if(n===null)return!0;var t=J.call(n,"constructor")&&n.constructor;return typeof t=="function"&&t instanceof t&&kt.call(t)==qs}var ka=wi?je(wi):function(e){return ue(e)&&we(e)==Xn},io=bi?je(bi):function(e){return ue(e)&&_e(e)==He};function sr(e){return typeof e=="string"||!D(e)&&ue(e)&&we(e)==Qn}function Oe(e){return typeof e=="symbol"||ue(e)&&we(e)==bt}var Zn=Si?je(Si):function(e){return ue(e)&&or(e.length)&&!!Q[we(e)]},Vc=Xt(aa),Zc=Xt(function(e,n){return e<=n});function uo(e){if(!e)return[];if(Me(e))return sr(e)?Ge(e):Ae(e);if(rt&&e[rt])return function(t){for(var r,i=[];!(r=t.next()).done;)i.push(r.value);return i}(e[rt]());var n=_e(e);return(n==Ne?Hr:n==He?It:Kn)(e)}function ln(e){return e?(e=$e(e))===re||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:e===0?e:0}function $(e){var n=ln(e),t=n%1;return n==n?t?n-t:n:0}function oo(e){return e?Cn($(e),0,N):0}function $e(e){if(typeof e=="number")return e;if(Oe(e))return U;if(ae(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=ae(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=Ci(e);var t=Yo.test(e);return t||Qo.test(e)?_s(e.slice(2),t?2:8):Jo.test(e)?U:+e}function so(e){return Xe(e,Ie(e))}function K(e){return e==null?"":qe(e)}var Kc=Hn(function(e,n){if(pt(n)||Me(n))Xe(n,pe(n),e);else for(var t in n)J.call(n,t)&&st(e,t,n[t])}),co=Hn(function(e,n){Xe(n,Ie(n),e)}),cr=Hn(function(e,n,t,r){Xe(n,Ie(n),e,r)}),Jc=Hn(function(e,n,t,r){Xe(n,pe(n),e,r)}),Yc=on(Yr),Xc=B(function(e,n){e=ee(e);var t=-1,r=n.length,i=r>2?n[2]:a;for(i&&be(n[0],n[1],i)&&(r=1);++t<r;)for(var s=n[t],c=Ie(s),f=-1,h=c.length;++f<h;){var y=c[f],g=e[y];(g===a||Ze(g,Dn[y])&&!J.call(e,y))&&(e[y]=s[y])}return e}),Qc=B(function(e){return e.push(a,Cu),Ee(lo,a,e)});function Ea(e,n,t){var r=e==null?a:Tn(e,n);return r===a?t:r}function ja(e,n){return e!=null&&Eu(e,n,nc)}var el=xu(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Et.call(n)),e[n]=t},Oa(Ce)),nl=xu(function(e,n,t){n!=null&&typeof n.toString!="function"&&(n=Et.call(n)),J.call(e,n)?e[n].push(t):e[n]=[t]},q),tl=B(lt);function pe(e){return Me(e)?Di(e):ra(e)}function Ie(e){return Me(e)?Di(e,!0):tc(e)}var rl=Hn(function(e,n,t){Ht(e,n,t)}),lo=Hn(function(e,n,t,r){Ht(e,n,t,r)}),al=on(function(e,n){var t={};if(e==null)return t;var r=!1;n=te(n,function(s){return s=_n(s,e),r||(r=s.length>1),s}),Xe(e,va(e),t),r&&(t=ze(t,7,lc));for(var i=n.length;i--;)ca(t,n[i]);return t}),il=on(function(e,n){return e==null?{}:function(t,r){return au(t,r,function(i,s){return ja(t,s)})}(e,n)});function fo(e,n){if(e==null)return{};var t=te(va(e),function(r){return[r]});return n=q(n),au(e,t,function(r,i){return n(r,i[0])})}var ho=Mu(pe),po=Mu(Ie);function Kn(e){return e==null?[]:Nr(e,pe(e))}var ul=Gn(function(e,n,t){return n=n.toLowerCase(),e+(t?go(n):n)});function go(e){return qa(K(e).toLowerCase())}function yo(e){return(e=K(e))&&e.replace(ns,As).replace(ls,"")}var ol=Gn(function(e,n,t){return e+(t?"-":"")+n.toLowerCase()}),sl=Gn(function(e,n,t){return e+(t?" ":"")+n.toLowerCase()}),cl=wu("toLowerCase"),ll=Gn(function(e,n,t){return e+(t?"_":"")+n.toLowerCase()}),fl=Gn(function(e,n,t){return e+(t?" ":"")+qa(n)}),hl=Gn(function(e,n,t){return e+(t?" ":"")+n.toUpperCase()}),qa=wu("toUpperCase");function vo(e,n,t){return e=K(e),(n=t?a:n)===a?function(r){return ds.test(r)}(e)?function(r){return r.match(fs)||[]}(e):function(r){return r.match(Go)||[]}(e):e.match(n)||[]}var _o=B(function(e,n){try{return Ee(e,a,n)}catch(t){return Ta(t)?t:new V(t)}}),dl=on(function(e,n){return Fe(n,function(t){t=Qe(t),an(e,t,Ia(e[t],e))}),e});function Oa(e){return function(){return e}}var pl=Su(),gl=Su(!0);function Ce(e){return e}function Pa(e){return Xi(typeof e=="function"?e:ze(e,1))}var yl=B(function(e,n){return function(t){return lt(t,e,n)}}),vl=B(function(e,n){return function(t){return lt(e,t,n)}});function Fa(e,n,t){var r=pe(n),i=Nt(n,r);t!=null||ae(n)&&(i.length||!r.length)||(t=n,n=e,e=this,i=Nt(n,pe(n)));var s=!(ae(t)&&"chain"in t&&!t.chain),c=cn(e);return Fe(i,function(f){var h=n[f];e[f]=h,c&&(e.prototype[f]=function(){var y=this.__chain__;if(s||y){var g=e(this.__wrapped__);return(g.__actions__=Ae(this.__actions__)).push({func:h,args:arguments,thisArg:e}),g.__chain__=y,g}return h.apply(e,dn([this.value()],arguments))})}),e}function Ua(){}var _l=pa(te),ml=pa(xi),wl=pa(zr);function mo(e){return ba(e)?Wr(Qe(e)):function(n){return function(t){return Tn(t,n)}}(e)}var bl=Ru(),Sl=Ru(!0);function La(){return[]}function za(){return!1}var Wa,xl=Jt(function(e,n){return e+n},0),Rl=ga("ceil"),Al=Jt(function(e,n){return e/n},1),Ml=ga("floor"),Il=Jt(function(e,n){return e*n},1),Cl=ga("round"),Tl=Jt(function(e,n){return e-n},0);return u.after=function(e,n){if(typeof n!="function")throw new Ue(o);return e=$(e),function(){if(--e<1)return n.apply(this,arguments)}},u.ary=Yu,u.assign=Kc,u.assignIn=co,u.assignInWith=cr,u.assignWith=Jc,u.at=Yc,u.before=Xu,u.bind=Ia,u.bindAll=dl,u.bindKey=Qu,u.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return D(e)?e:[e]},u.chain=Zu,u.chunk=function(e,n,t){n=(t?be(e,n,t):n===a)?1:he($(n),0);var r=e==null?0:e.length;if(!r||n<1)return[];for(var i=0,s=0,c=I(Ut(r/n));i<r;)c[s++]=We(e,i,i+=n);return c},u.compact=function(e){for(var n=-1,t=e==null?0:e.length,r=0,i=[];++n<t;){var s=e[n];s&&(i[r++]=s)}return i},u.concat=function(){var e=arguments.length;if(!e)return[];for(var n=I(e-1),t=arguments[0],r=e;r--;)n[r-1]=arguments[r];return dn(D(t)?Ae(t):[t],ye(n,1))},u.cond=function(e){var n=e==null?0:e.length,t=q();return e=n?te(e,function(r){if(typeof r[1]!="function")throw new Ue(o);return[t(r[0]),r[1]]}):[],B(function(r){for(var i=-1;++i<n;){var s=e[i];if(Ee(s[0],this,r))return Ee(s[1],this,r)}})},u.conforms=function(e){return function(n){var t=pe(n);return function(r){return Ni(r,n,t)}}(ze(e,1))},u.constant=Oa,u.countBy=Ec,u.create=function(e,n){var t=Nn(e);return n==null?t:Bi(t,n)},u.curry=function e(n,t,r){var i=un(n,8,a,a,a,a,a,t=r?a:t);return i.placeholder=e.placeholder,i},u.curryRight=function e(n,t,r){var i=un(n,b,a,a,a,a,a,t=r?a:t);return i.placeholder=e.placeholder,i},u.debounce=eo,u.defaults=Xc,u.defaultsDeep=Qc,u.defer=zc,u.delay=Wc,u.difference=dc,u.differenceBy=pc,u.differenceWith=gc,u.drop=function(e,n,t){var r=e==null?0:e.length;return r?We(e,(n=t||n===a?1:$(n))<0?0:n,r):[]},u.dropRight=function(e,n,t){var r=e==null?0:e.length;return r?We(e,0,(n=r-(n=t||n===a?1:$(n)))<0?0:n):[]},u.dropRightWhile=function(e,n){return e&&e.length?Vt(e,q(n,3),!0,!0):[]},u.dropWhile=function(e,n){return e&&e.length?Vt(e,q(n,3),!0):[]},u.fill=function(e,n,t,r){var i=e==null?0:e.length;return i?(t&&typeof t!="number"&&be(e,n,t)&&(t=0,r=i),function(s,c,f,h){var y=s.length;for((f=$(f))<0&&(f=-f>y?0:y+f),(h=h===a||h>y?y:$(h))<0&&(h+=y),h=f>h?0:oo(h);f<h;)s[f++]=c;return s}(e,n,t,r)):[]},u.filter=function(e,n){return(D(e)?hn:Vi)(e,q(n,3))},u.flatMap=function(e,n){return ye(rr(e,n),1)},u.flatMapDeep=function(e,n){return ye(rr(e,n),re)},u.flatMapDepth=function(e,n,t){return t=t===a?1:$(t),ye(rr(e,n),t)},u.flatten=Nu,u.flattenDeep=function(e){return e!=null&&e.length?ye(e,re):[]},u.flattenDepth=function(e,n){return e!=null&&e.length?ye(e,n=n===a?1:$(n)):[]},u.flip=function(e){return un(e,512)},u.flow=pl,u.flowRight=gl,u.fromPairs=function(e){for(var n=-1,t=e==null?0:e.length,r={};++n<t;){var i=e[n];r[i[0]]=i[1]}return r},u.functions=function(e){return e==null?[]:Nt(e,pe(e))},u.functionsIn=function(e){return e==null?[]:Nt(e,Ie(e))},u.groupBy=Oc,u.initial=function(e){return e!=null&&e.length?We(e,0,-1):[]},u.intersection=yc,u.intersectionBy=vc,u.intersectionWith=_c,u.invert=el,u.invertBy=nl,u.invokeMap=Pc,u.iteratee=Pa,u.keyBy=Fc,u.keys=pe,u.keysIn=Ie,u.map=rr,u.mapKeys=function(e,n){var t={};return n=q(n,3),Ye(e,function(r,i,s){an(t,n(r,i,s),r)}),t},u.mapValues=function(e,n){var t={};return n=q(n,3),Ye(e,function(r,i,s){an(t,i,n(r,i,s))}),t},u.matches=function(e){return eu(ze(e,1))},u.matchesProperty=function(e,n){return nu(e,ze(n,1))},u.memoize=ir,u.merge=rl,u.mergeWith=lo,u.method=yl,u.methodOf=vl,u.mixin=Fa,u.negate=ur,u.nthArg=function(e){return e=$(e),B(function(n){return tu(n,e)})},u.omit=al,u.omitBy=function(e,n){return fo(e,ur(q(n)))},u.once=function(e){return Xu(2,e)},u.orderBy=function(e,n,t,r){return e==null?[]:(D(n)||(n=n==null?[]:[n]),D(t=r?a:t)||(t=t==null?[]:[t]),ru(e,n,t))},u.over=_l,u.overArgs=Dc,u.overEvery=ml,u.overSome=wl,u.partial=Ca,u.partialRight=no,u.partition=Uc,u.pick=il,u.pickBy=fo,u.property=mo,u.propertyOf=function(e){return function(n){return e==null?a:Tn(e,n)}},u.pull=mc,u.pullAll=Gu,u.pullAllBy=function(e,n,t){return e&&e.length&&n&&n.length?ia(e,n,q(t,2)):e},u.pullAllWith=function(e,n,t){return e&&e.length&&n&&n.length?ia(e,n,a,t):e},u.pullAt=wc,u.range=bl,u.rangeRight=Sl,u.rearg=$c,u.reject=function(e,n){return(D(e)?hn:Vi)(e,ur(q(n,3)))},u.remove=function(e,n){var t=[];if(!e||!e.length)return t;var r=-1,i=[],s=e.length;for(n=q(n,3);++r<s;){var c=e[r];n(c,r,e)&&(t.push(c),i.push(r))}return iu(e,i),t},u.rest=function(e,n){if(typeof e!="function")throw new Ue(o);return B(e,n=n===a?n:$(n))},u.reverse=Aa,u.sampleSize=function(e,n,t){return n=(t?be(e,n,t):n===a)?1:$(n),(D(e)?Js:ac)(e,n)},u.set=function(e,n,t){return e==null?e:ht(e,n,t)},u.setWith=function(e,n,t,r){return r=typeof r=="function"?r:a,e==null?e:ht(e,n,t,r)},u.shuffle=function(e){return(D(e)?Ys:uc)(e)},u.slice=function(e,n,t){var r=e==null?0:e.length;return r?(t&&typeof t!="number"&&be(e,n,t)?(n=0,t=r):(n=n==null?0:$(n),t=t===a?r:$(t)),We(e,n,t)):[]},u.sortBy=Lc,u.sortedUniq=function(e){return e&&e.length?ou(e):[]},u.sortedUniqBy=function(e,n){return e&&e.length?ou(e,q(n,2)):[]},u.split=function(e,n,t){return t&&typeof t!="number"&&be(e,n,t)&&(n=t=a),(t=t===a?N:t>>>0)?(e=K(e))&&(typeof n=="string"||n!=null&&!ka(n))&&!(n=qe(n))&&Ln(e)?mn(Ge(e),0,t):e.split(n,t):[]},u.spread=function(e,n){if(typeof e!="function")throw new Ue(o);return n=n==null?0:he($(n),0),B(function(t){var r=t[n],i=mn(t,0,n);return r&&dn(i,r),Ee(e,this,i)})},u.tail=function(e){var n=e==null?0:e.length;return n?We(e,1,n):[]},u.take=function(e,n,t){return e&&e.length?We(e,0,(n=t||n===a?1:$(n))<0?0:n):[]},u.takeRight=function(e,n,t){var r=e==null?0:e.length;return r?We(e,(n=r-(n=t||n===a?1:$(n)))<0?0:n,r):[]},u.takeRightWhile=function(e,n){return e&&e.length?Vt(e,q(n,3),!1,!0):[]},u.takeWhile=function(e,n){return e&&e.length?Vt(e,q(n,3)):[]},u.tap=function(e,n){return n(e),e},u.throttle=function(e,n,t){var r=!0,i=!0;if(typeof e!="function")throw new Ue(o);return ae(t)&&(r="leading"in t?!!t.leading:r,i="trailing"in t?!!t.trailing:i),eo(e,n,{leading:r,maxWait:n,trailing:i})},u.thru=tr,u.toArray=uo,u.toPairs=ho,u.toPairsIn=po,u.toPath=function(e){return D(e)?te(e,Qe):Oe(e)?[e]:Ae(Wu(K(e)))},u.toPlainObject=so,u.transform=function(e,n,t){var r=D(e),i=r||wn(e)||Zn(e);if(n=q(n,4),t==null){var s=e&&e.constructor;t=i?r?new s:[]:ae(e)&&cn(s)?Nn(Ot(e)):{}}return(i?Fe:Ye)(e,function(c,f,h){return n(t,c,f,h)}),t},u.unary=function(e){return Yu(e,1)},u.union=bc,u.unionBy=Sc,u.unionWith=xc,u.uniq=function(e){return e&&e.length?vn(e):[]},u.uniqBy=function(e,n){return e&&e.length?vn(e,q(n,2)):[]},u.uniqWith=function(e,n){return n=typeof n=="function"?n:a,e&&e.length?vn(e,a,n):[]},u.unset=function(e,n){return e==null||ca(e,n)},u.unzip=Ma,u.unzipWith=Vu,u.update=function(e,n,t){return e==null?e:cu(e,n,ha(t))},u.updateWith=function(e,n,t,r){return r=typeof r=="function"?r:a,e==null?e:cu(e,n,ha(t),r)},u.values=Kn,u.valuesIn=function(e){return e==null?[]:Nr(e,Ie(e))},u.without=Rc,u.words=vo,u.wrap=function(e,n){return Ca(ha(n),e)},u.xor=Ac,u.xorBy=Mc,u.xorWith=Ic,u.zip=Cc,u.zipObject=function(e,n){return fu(e||[],n||[],st)},u.zipObjectDeep=function(e,n){return fu(e||[],n||[],ht)},u.zipWith=Tc,u.entries=ho,u.entriesIn=po,u.extend=co,u.extendWith=cr,Fa(u,u),u.add=xl,u.attempt=_o,u.camelCase=ul,u.capitalize=go,u.ceil=Rl,u.clamp=function(e,n,t){return t===a&&(t=n,n=a),t!==a&&(t=(t=$e(t))==t?t:0),n!==a&&(n=(n=$e(n))==n?n:0),Cn($e(e),n,t)},u.clone=function(e){return ze(e,4)},u.cloneDeep=function(e){return ze(e,5)},u.cloneDeepWith=function(e,n){return ze(e,5,n=typeof n=="function"?n:a)},u.cloneWith=function(e,n){return ze(e,4,n=typeof n=="function"?n:a)},u.conformsTo=function(e,n){return n==null||Ni(e,n,pe(n))},u.deburr=yo,u.defaultTo=function(e,n){return e==null||e!=e?n:e},u.divide=Al,u.endsWith=function(e,n,t){e=K(e),n=qe(n);var r=e.length,i=t=t===a?r:Cn($(t),0,r);return(t-=n.length)>=0&&e.slice(t,i)==n},u.eq=Ze,u.escape=function(e){return(e=K(e))&&Po.test(e)?e.replace(Za,Ms):e},u.escapeRegExp=function(e){return(e=K(e))&&Do.test(e)?e.replace(Tr,"\\$&"):e},u.every=function(e,n,t){var r=D(e)?xi:Qs;return t&&be(e,n,t)&&(n=a),r(e,q(n,3))},u.find=jc,u.findIndex=$u,u.findKey=function(e,n){return Ri(e,q(n,3),Ye)},u.findLast=qc,u.findLastIndex=Bu,u.findLastKey=function(e,n){return Ri(e,q(n,3),Qr)},u.floor=Ml,u.forEach=Ku,u.forEachRight=Ju,u.forIn=function(e,n){return e==null?e:Xr(e,q(n,3),Ie)},u.forInRight=function(e,n){return e==null?e:Zi(e,q(n,3),Ie)},u.forOwn=function(e,n){return e&&Ye(e,q(n,3))},u.forOwnRight=function(e,n){return e&&Qr(e,q(n,3))},u.get=Ea,u.gt=Bc,u.gte=Nc,u.has=function(e,n){return e!=null&&Eu(e,n,ec)},u.hasIn=ja,u.head=Hu,u.identity=Ce,u.includes=function(e,n,t,r){e=Me(e)?e:Kn(e),t=t&&!r?$(t):0;var i=e.length;return t<0&&(t=he(i+t,0)),sr(e)?t<=i&&e.indexOf(n,t)>-1:!!i&&Un(e,n,t)>-1},u.indexOf=function(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=t==null?0:$(t);return i<0&&(i=he(r+i,0)),Un(e,n,i)},u.inRange=function(e,n,t){return n=ln(n),t===a?(t=n,n=0):t=ln(t),function(r,i,s){return r>=ve(i,s)&&r<he(i,s)}(e=$e(e),n,t)},u.invoke=tl,u.isArguments=jn,u.isArray=D,u.isArrayBuffer=Hc,u.isArrayLike=Me,u.isArrayLikeObject=ce,u.isBoolean=function(e){return e===!0||e===!1||ue(e)&&we(e)==Y},u.isBuffer=wn,u.isDate=Gc,u.isElement=function(e){return ue(e)&&e.nodeType===1&&!yt(e)},u.isEmpty=function(e){if(e==null)return!0;if(Me(e)&&(D(e)||typeof e=="string"||typeof e.splice=="function"||wn(e)||Zn(e)||jn(e)))return!e.length;var n=_e(e);if(n==Ne||n==He)return!e.size;if(pt(e))return!ra(e).length;for(var t in e)if(J.call(e,t))return!1;return!0},u.isEqual=function(e,n){return ft(e,n)},u.isEqualWith=function(e,n,t){var r=(t=typeof t=="function"?t:a)?t(e,n):a;return r===a?ft(e,n,a,t):!!r},u.isError=Ta,u.isFinite=function(e){return typeof e=="number"&&Li(e)},u.isFunction=cn,u.isInteger=to,u.isLength=or,u.isMap=ro,u.isMatch=function(e,n){return e===n||ta(e,n,ma(n))},u.isMatchWith=function(e,n,t){return t=typeof t=="function"?t:a,ta(e,n,ma(n),t)},u.isNaN=function(e){return ao(e)&&e!=+e},u.isNative=function(e){if(hc(e))throw new V("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Yi(e)},u.isNil=function(e){return e==null},u.isNull=function(e){return e===null},u.isNumber=ao,u.isObject=ae,u.isObjectLike=ue,u.isPlainObject=yt,u.isRegExp=ka,u.isSafeInteger=function(e){return to(e)&&e>=-9007199254740991&&e<=oe},u.isSet=io,u.isString=sr,u.isSymbol=Oe,u.isTypedArray=Zn,u.isUndefined=function(e){return e===a},u.isWeakMap=function(e){return ue(e)&&_e(e)==et},u.isWeakSet=function(e){return ue(e)&&we(e)=="[object WeakSet]"},u.join=function(e,n){return e==null?"":Ws.call(e,n)},u.kebabCase=ol,u.last=De,u.lastIndexOf=function(e,n,t){var r=e==null?0:e.length;if(!r)return-1;var i=r;return t!==a&&(i=(i=$(t))<0?he(r+i,0):ve(i,r-1)),n==n?function(s,c,f){for(var h=f+1;h--;)if(s[h]===c)return h;return h}(e,n,i):Mt(e,Ai,i,!0)},u.lowerCase=sl,u.lowerFirst=cl,u.lt=Vc,u.lte=Zc,u.max=function(e){return e&&e.length?Bt(e,Ce,ea):a},u.maxBy=function(e,n){return e&&e.length?Bt(e,q(n,2),ea):a},u.mean=function(e){return Mi(e,Ce)},u.meanBy=function(e,n){return Mi(e,q(n,2))},u.min=function(e){return e&&e.length?Bt(e,Ce,aa):a},u.minBy=function(e,n){return e&&e.length?Bt(e,q(n,2),aa):a},u.stubArray=La,u.stubFalse=za,u.stubObject=function(){return{}},u.stubString=function(){return""},u.stubTrue=function(){return!0},u.multiply=Il,u.nth=function(e,n){return e&&e.length?tu(e,$(n)):a},u.noConflict=function(){return ge._===this&&(ge._=Os),this},u.noop=Ua,u.now=ar,u.pad=function(e,n,t){e=K(e);var r=(n=$(n))?zn(e):0;if(!n||r>=n)return e;var i=(n-r)/2;return Yt(Lt(i),t)+e+Yt(Ut(i),t)},u.padEnd=function(e,n,t){e=K(e);var r=(n=$(n))?zn(e):0;return n&&r<n?e+Yt(n-r,t):e},u.padStart=function(e,n,t){e=K(e);var r=(n=$(n))?zn(e):0;return n&&r<n?Yt(n-r,t)+e:e},u.parseInt=function(e,n,t){return t||n==null?n=0:n&&(n=+n),Bs(K(e).replace(kr,""),n||0)},u.random=function(e,n,t){if(t&&typeof t!="boolean"&&be(e,n,t)&&(n=t=a),t===a&&(typeof n=="boolean"?(t=n,n=a):typeof e=="boolean"&&(t=e,e=a)),e===a&&n===a?(e=0,n=1):(e=ln(e),n===a?(n=e,e=0):n=ln(n)),e>n){var r=e;e=n,n=r}if(t||e%1||n%1){var i=zi();return ve(e+i*(n-e+vs("1e-"+((i+"").length-1))),n)}return ua(e,n)},u.reduce=function(e,n,t){var r=D(e)?Lr:Ii,i=arguments.length<3;return r(e,q(n,4),t,i,yn)},u.reduceRight=function(e,n,t){var r=D(e)?Ss:Ii,i=arguments.length<3;return r(e,q(n,4),t,i,Gi)},u.repeat=function(e,n,t){return n=(t?be(e,n,t):n===a)?1:$(n),oa(K(e),n)},u.replace=function(){var e=arguments,n=K(e[0]);return e.length<3?n:n.replace(e[1],e[2])},u.result=function(e,n,t){var r=-1,i=(n=_n(n,e)).length;for(i||(i=1,e=a);++r<i;){var s=e==null?a:e[Qe(n[r])];s===a&&(r=i,s=t),e=cn(s)?s.call(e):s}return e},u.round=Cl,u.runInContext=p,u.sample=function(e){return(D(e)?$i:rc)(e)},u.size=function(e){if(e==null)return 0;if(Me(e))return sr(e)?zn(e):e.length;var n=_e(e);return n==Ne||n==He?e.size:ra(e).length},u.snakeCase=ll,u.some=function(e,n,t){var r=D(e)?zr:oc;return t&&be(e,n,t)&&(n=a),r(e,q(n,3))},u.sortedIndex=function(e,n){return Gt(e,n)},u.sortedIndexBy=function(e,n,t){return sa(e,n,q(t,2))},u.sortedIndexOf=function(e,n){var t=e==null?0:e.length;if(t){var r=Gt(e,n);if(r<t&&Ze(e[r],n))return r}return-1},u.sortedLastIndex=function(e,n){return Gt(e,n,!0)},u.sortedLastIndexBy=function(e,n,t){return sa(e,n,q(t,2),!0)},u.sortedLastIndexOf=function(e,n){if(e!=null&&e.length){var t=Gt(e,n,!0)-1;if(Ze(e[t],n))return t}return-1},u.startCase=fl,u.startsWith=function(e,n,t){return e=K(e),t=t==null?0:Cn($(t),0,e.length),n=qe(n),e.slice(t,t+n.length)==n},u.subtract=Tl,u.sum=function(e){return e&&e.length?$r(e,Ce):0},u.sumBy=function(e,n){return e&&e.length?$r(e,q(n,2)):0},u.template=function(e,n,t){var r=u.templateSettings;t&&be(e,n,t)&&(n=a),e=K(e),n=cr({},n,r,Iu);var i,s,c=cr({},n.imports,r.imports,Iu),f=pe(c),h=Nr(c,f),y=0,g=n.interpolate||St,S="__p += '",C=Gr((n.escape||St).source+"|"+g.source+"|"+(g===Ka?Ko:St).source+"|"+(n.evaluate||St).source+"|$","g"),k="//# sourceURL="+(J.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++gs+"]")+`
`;e.replace(C,function(R,O,M,W,L,Z){return M||(M=W),S+=e.slice(y,Z).replace(ts,Is),O&&(i=!0,S+=`' +
__e(`+O+`) +
'`),L&&(s=!0,S+=`';
`+L+`;
__p += '`),M&&(S+=`' +
((__t = (`+M+`)) == null ? '' : __t) +
'`),y=Z+R.length,R}),S+=`';
`;var j=J.call(n,"variable")&&n.variable;if(j){if(Vo.test(j))throw new V("Invalid `variable` option passed into `_.template`")}else S=`with (obj) {
`+S+`
}
`;S=(s?S.replace(Eo,""):S).replace(jo,"$1").replace(qo,"$1;"),S="function("+(j||"obj")+`) {
`+(j?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(i?", __e = _.escape":"")+(s?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+S+`return __p
}`;var v=_o(function(){return fe(f,k+"return "+S).apply(a,h)});if(v.source=S,Ta(v))throw v;return v},u.times=function(e,n){if((e=$(e))<1||e>oe)return[];var t=N,r=ve(e,N);n=q(n),e-=N;for(var i=Br(r,n);++t<e;)n(t);return i},u.toFinite=ln,u.toInteger=$,u.toLength=oo,u.toLower=function(e){return K(e).toLowerCase()},u.toNumber=$e,u.toSafeInteger=function(e){return e?Cn($(e),-9007199254740991,oe):e===0?e:0},u.toString=K,u.toUpper=function(e){return K(e).toUpperCase()},u.trim=function(e,n,t){if((e=K(e))&&(t||n===a))return Ci(e);if(!e||!(n=qe(n)))return e;var r=Ge(e),i=Ge(n);return mn(r,Ti(r,i),ki(r,i)+1).join("")},u.trimEnd=function(e,n,t){if((e=K(e))&&(t||n===a))return e.slice(0,ji(e)+1);if(!e||!(n=qe(n)))return e;var r=Ge(e);return mn(r,0,ki(r,Ge(n))+1).join("")},u.trimStart=function(e,n,t){if((e=K(e))&&(t||n===a))return e.replace(kr,"");if(!e||!(n=qe(n)))return e;var r=Ge(e);return mn(r,Ti(r,Ge(n))).join("")},u.truncate=function(e,n){var t=30,r="...";if(ae(n)){var i="separator"in n?n.separator:i;t="length"in n?$(n.length):t,r="omission"in n?qe(n.omission):r}var s=(e=K(e)).length;if(Ln(e)){var c=Ge(e);s=c.length}if(t>=s)return e;var f=t-zn(r);if(f<1)return r;var h=c?mn(c,0,f).join(""):e.slice(0,f);if(i===a)return h+r;if(c&&(f+=h.length-f),ka(i)){if(e.slice(f).search(i)){var y,g=h;for(i.global||(i=Gr(i.source,K(Ja.exec(i))+"g")),i.lastIndex=0;y=i.exec(g);)var S=y.index;h=h.slice(0,S===a?f:S)}}else if(e.indexOf(qe(i),f)!=f){var C=h.lastIndexOf(i);C>-1&&(h=h.slice(0,C))}return h+r},u.unescape=function(e){return(e=K(e))&&Oo.test(e)?e.replace(Va,Ts):e},u.uniqueId=function(e){var n=++js;return K(e)+n},u.upperCase=hl,u.upperFirst=qa,u.each=Ku,u.eachRight=Ju,u.first=Hu,Fa(u,(Wa={},Ye(u,function(e,n){J.call(u.prototype,n)||(Wa[n]=e)}),Wa),{chain:!1}),u.VERSION="4.17.21",Fe(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){u[e].placeholder=u}),Fe(["drop","take"],function(e,n){H.prototype[e]=function(t){t=t===a?1:he($(t),0);var r=this.__filtered__&&!n?new H(this):this.clone();return r.__filtered__?r.__takeCount__=ve(t,r.__takeCount__):r.__views__.push({size:ve(t,N),type:e+(r.__dir__<0?"Right":"")}),r},H.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Fe(["filter","map","takeWhile"],function(e,n){var t=n+1,r=t==1||t==3;H.prototype[e]=function(i){var s=this.clone();return s.__iteratees__.push({iteratee:q(i,3),type:t}),s.__filtered__=s.__filtered__||r,s}}),Fe(["head","last"],function(e,n){var t="take"+(n?"Right":"");H.prototype[e]=function(){return this[t](1).value()[0]}}),Fe(["initial","tail"],function(e,n){var t="drop"+(n?"":"Right");H.prototype[e]=function(){return this.__filtered__?new H(this):this[t](1)}}),H.prototype.compact=function(){return this.filter(Ce)},H.prototype.find=function(e){return this.filter(e).head()},H.prototype.findLast=function(e){return this.reverse().find(e)},H.prototype.invokeMap=B(function(e,n){return typeof e=="function"?new H(this):this.map(function(t){return lt(t,e,n)})}),H.prototype.reject=function(e){return this.filter(ur(q(e)))},H.prototype.slice=function(e,n){e=$(e);var t=this;return t.__filtered__&&(e>0||n<0)?new H(t):(e<0?t=t.takeRight(-e):e&&(t=t.drop(e)),n!==a&&(t=(n=$(n))<0?t.dropRight(-n):t.take(n-e)),t)},H.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},H.prototype.toArray=function(){return this.take(N)},Ye(H.prototype,function(e,n){var t=/^(?:filter|find|map|reject)|While$/.test(n),r=/^(?:head|last)$/.test(n),i=u[r?"take"+(n=="last"?"Right":""):n],s=r||/^find/.test(n);i&&(u.prototype[n]=function(){var c=this.__wrapped__,f=r?[1]:arguments,h=c instanceof H,y=f[0],g=h||D(c),S=function(O){var M=i.apply(u,dn([O],f));return r&&C?M[0]:M};g&&t&&typeof y=="function"&&y.length!=1&&(h=g=!1);var C=this.__chain__,k=!!this.__actions__.length,j=s&&!C,v=h&&!k;if(!s&&g){c=v?c:new H(this);var R=e.apply(c,f);return R.__actions__.push({func:tr,args:[S],thisArg:a}),new Le(R,C)}return j&&v?e.apply(this,f):(R=this.thru(S),j?r?R.value()[0]:R.value():R)})}),Fe(["pop","push","shift","sort","splice","unshift"],function(e){var n=Ct[e],t=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);u.prototype[e]=function(){var i=arguments;if(r&&!this.__chain__){var s=this.value();return n.apply(D(s)?s:[],i)}return this[t](function(c){return n.apply(D(c)?c:[],i)})}}),Ye(H.prototype,function(e,n){var t=u[n];if(t){var r=t.name+"";J.call(Bn,r)||(Bn[r]=[]),Bn[r].push({name:n,func:t})}}),Bn[Kt(a,2).name]=[{name:"wrapper",func:a}],H.prototype.clone=function(){var e=new H(this.__wrapped__);return e.__actions__=Ae(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ae(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ae(this.__views__),e},H.prototype.reverse=function(){if(this.__filtered__){var e=new H(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},H.prototype.value=function(){var e=this.__wrapped__.value(),n=this.__dir__,t=D(e),r=n<0,i=t?e.length:0,s=function(Z,E,P){for(var de=-1,le=P.length;++de<le;){var Se=P[de],ne=Se.size;switch(Se.type){case"drop":Z+=ne;break;case"dropRight":E-=ne;break;case"take":E=ve(E,Z+ne);break;case"takeRight":Z=he(Z,E-ne)}}return{start:Z,end:E}}(0,i,this.__views__),c=s.start,f=s.end,h=f-c,y=r?f:c-1,g=this.__iteratees__,S=g.length,C=0,k=ve(h,this.__takeCount__);if(!t||!r&&i==h&&k==h)return lu(e,this.__actions__);var j=[];e:for(;h--&&C<k;){for(var v=-1,R=e[y+=n];++v<S;){var O=g[v],M=O.iteratee,W=O.type,L=M(R);if(W==2)R=L;else if(!L){if(W==1)continue e;break e}}j[C++]=R}return j},u.prototype.at=kc,u.prototype.chain=function(){return Zu(this)},u.prototype.commit=function(){return new Le(this.value(),this.__chain__)},u.prototype.next=function(){this.__values__===a&&(this.__values__=uo(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?a:this.__values__[this.__index__++]}},u.prototype.plant=function(e){for(var n,t=this;t instanceof Dt;){var r=Du(t);r.__index__=0,r.__values__=a,n?i.__wrapped__=r:n=r;var i=r;t=t.__wrapped__}return i.__wrapped__=e,n},u.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof H){var n=e;return this.__actions__.length&&(n=new H(this)),(n=n.reverse()).__actions__.push({func:tr,args:[Aa],thisArg:a}),new Le(n,this.__chain__)}return this.thru(Aa)},u.prototype.toJSON=u.prototype.valueOf=u.prototype.value=function(){return lu(this.__wrapped__,this.__actions__)},u.prototype.first=u.prototype.head,rt&&(u.prototype[rt]=function(){return this}),u}();Rn?((Rn.exports=Wn)._=Wn,Pr._=Wn):ge._=Wn}).call(_t);var Zf=Na.exports;export{Jl as A,Nf as B,Yl as C,cf as D,Te as E,lf as F,ff as G,Tf as H,Pf as I,Wf as J,Gf as K,Hf as L,Uf as M,$f as N,Bf as O,Lf as P,Ke as S,Df as U,Rf as a,If as b,Mf as c,bf as d,Sf as e,xf as f,Xl as g,Af as h,Of as i,Cf as j,kf as k,Zf as l,qf as m,Vf as n,jf as o,Ef as p,wf as q,sf as r,mf as s,tf as t,rf as u,Ff as v,af as w,uf as x,of as y,zf as z};
