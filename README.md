# Augment 验证规避工具

这个工具用于帮助规避 Augment VSCode 扩展的许可证验证机制，允许使用全部功能。

## 工作原理

Augment 扩展使用多种机制验证许可：
1. 会话 ID（sessionId）- 用于识别用户会话
2. 机器 ID - 用于识别设备
3. 虚拟环境检测 - 检测是否在虚拟机中运行

这个工具通过修改 Augment 扩展的会话 ID 和相关验证机制，帮助规避这些限制。

## 提供的脚本

本项目提供了两个脚本：

1. `augment-spoof-simple.js` - 简化版，仅修改会话 ID（适合大多数情况）
2. `augment-spoof.js` - 增强版，尝试修改会话 ID、机器 ID 和系统信息

## 安装说明

### 方法一：直接在 VSCode 中使用

1. 打开 VSCode
2. 按 F1 或 Ctrl+Shift+P 打开命令面板
3. 输入 `>Developer: Open User Profile Folder` 并执行
4. 在打开的目录中，新建或修改 `.vscode/extensions/extensions.js` 文件
5. 将选择的脚本内容复制到此文件中
6. 重启 VSCode

### 方法二：定制 VSCode 启动脚本

1. 创建一个目录用于存放脚本，例如 `C:\VSCodeScript`
2. 将选择的脚本复制到该目录
3. 创建一个批处理文件 `start-vscode.bat`，内容如下：

```batch
@echo off
set NODE_OPTIONS=--require C:\VSCodeScript\augment-spoof-simple.js
start "" "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe"
```

4. 修改上述路径以匹配你的环境
5. 使用此批处理文件启动 VSCode

## 使用验证

脚本加载后，你应该能在 VSCode 的输出控制台中看到以下日志：

```
[augment-spoof] 文件已加载
[augment-spoof] Augment扩展尚未激活，等待激活事件
[augment-spoof] Augment扩展已激活，立即修补
[augment-spoof] sessionId 已修改为: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
```

如果看到这些日志，表示修改成功。

## 故障排除

如果遇到问题：

1. 检查 VSCode 输出面板中是否有错误日志
2. 尝试使用增强版脚本 `augment-spoof.js`
3. 确保使用的是最新版本的脚本

## 注意事项

- 脚本需要在 Augment 扩展加载前执行才能生效
- 每次 VSCode 启动时都需要加载脚本
- 这个方法可能会随着 Augment 扩展更新而失效
- 使用此脚本可能违反 Augment 的使用条款 