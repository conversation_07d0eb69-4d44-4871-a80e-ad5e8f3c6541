/**
 * augment-spoof-simple.js - Augment VSCode扩展验证规避脚本(简化版)
 * 
 * 该脚本仅修改Augment扩展的会话ID，简单但有效
 */

// 打印调试信息
console.log('[augment-spoof] 文件已加载');

// 当所有扩展都激活后再 patch，避免时序问题
const { extensions } = require('vscode');

// 监听扩展变化事件
extensions.onDidChange(() => {
  // 获取Augment扩展
  const aug = extensions.getExtension('augment.vscode-augment');
  if (!aug || !aug.isActive) return;           // 还未激活

  try {
    // 获取API服务器实例
    const api = aug.exports?._apiServer;
    if (!api || api.__patched) return;         // 已处理过

    // 生成一个新的随机ID
    const crypto = require('crypto');
    const newId = crypto.randomUUID?.() ||     // Node ≥ 14.17
      ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,
        c => (c ^ crypto.randomBytes(1)[0] & 15 >> c/4).toString(16));

    // 修改sessionId属性
    Object.defineProperty(api, 'sessionId', {
      get() { return newId; },
      set() {}, // 忽略设置操作
      configurable: false // 防止被修改
    });
    
    // 标记已修补并打印日志
    api.__patched = true;
    console.log('[augment-spoof] sessionId 已修改为:', newId);
  } catch (e) {
    // 详细的错误日志
    console.error('[augment-spoof] 修补失败:', e);
    if (e.stack) {
      console.error('[augment-spoof] 错误堆栈:', e.stack);
    }
  }
});

// 添加初始检查，加载扩展时立即尝试修补
try {
  setTimeout(() => {
    const aug = extensions.getExtension('augment.vscode-augment');
    if (aug && aug.isActive) {
      console.log('[augment-spoof] Augment扩展已激活，立即修补');
      
      // 手动触发一次onDidChange事件
      const api = aug.exports?._apiServer;
      if (api && !api.__patched) {
        extensions.onDidChange.emit();
      }
    } else {
      console.log('[augment-spoof] Augment扩展尚未激活，等待激活事件');
    }
  }, 1000); // 1秒后检查
} catch (e) {
  console.error('[augment-spoof] 初始检查失败:', e);
} 