# Augment VSCode扩展安全验证机制分析

本文档详细分析了Augment VSCode扩展中使用的安全验证机制，特别是机器码验证和虚拟机检测方面。

## 1. 机器码获取机制

Augment扩展使用`electron-machine-id`库来获取设备的唯一标识符。这个库根据不同的操作系统平台使用不同的命令和方法来获取机器ID。

### 1.1 各平台获取机器ID的具体命令

```javascript
var v = {
  darwin: "ioreg -rd1 -c IOPlatformExpertDevice",  // macOS
  win32: b[o()] + "\\REG.exe QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid",  // Windows
  linux: "( cat /var/lib/dbus/machine-id /etc/machine-id 2> /dev/null || hostname ) | head -n 1 || :",  // Linux
  freebsd: "kenv -q smbios.system.uuid || sysctl -n kern.hostuuid"  // FreeBSD
};
```

### 1.2 各平台具体实现方式

#### Windows平台
- 使用Windows注册表读取机器GUID
- 命令: `REG.exe QUERY HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography /v MachineGuid`
- 通过`child_process`模块执行此命令
- 从输出结果中提取MachineGuid值

#### macOS平台
- 使用`ioreg`命令获取IOPlatformExpertDevice信息
- 命令: `ioreg -rd1 -c IOPlatformExpertDevice`
- 通过`child_process`模块执行此命令
- 从输出结果中解析提取设备唯一标识

#### Linux平台
- 尝试从多个位置读取机器ID
- 首先尝试读取`/var/lib/dbus/machine-id`
- 如果不存在，则尝试读取`/etc/machine-id`
- 如果都不存在，则使用`hostname`命令
- 命令: `( cat /var/lib/dbus/machine-id /etc/machine-id 2> /dev/null || hostname ) | head -n 1 || :`

### 1.3 机器ID同步与异步获取方法

扩展提供了两种获取机器ID的方法：
- `machineIdSync()`: 同步方法，直接返回机器ID
- `machineId()`: 异步方法，通过Promise返回机器ID

### 1.4 代码调用示例

在扩展中，我们可以看到如下调用：

```javascript
n[2] = (0, p4e.machineIdSync)()
```

这表明扩展使用同步方法获取机器ID，并将结果存储在某个数组中，可能用于后续的许可证验证或其他安全检查。

### 1.5 机器ID的存储与发送

获取到的机器ID不会直接明文存储在本地文件系统中，而是：

1. 通过SHA-256哈希算法处理：
   ```javascript
   function machineIdSync(original = false){
       const raw = cleanup(execSync(v[platform]).toString());
       return original ? raw : sha256(raw);
   }
   ```

2. 存储在内存中，作为API请求的一部分发送到服务器。通过代码分析，机器ID被存储在一个数组`n[2]`中：
   ```javascript
   n[2] = (0, p4e.machineIdSync)()
   ```

3. 在与服务器通信时，机器ID会与其他信息一起通过`callApi`函数发送到服务器。虽然代码中没有明确将机器ID作为参数名的部分，但通过分析可以推断机器ID被包含在请求中，特别是在获取订阅信息时：
   ```javascript
   async getSubscriptionInfo() {
     let r = this.createRequestId();
     let n = this._configListener.config;
     return await this.callApi(r, n, "subscription-info", {});
   }
   ```

4. 扩展使用`globalState`API将一些信息持久化存储，但机器ID本身不会直接保存到本地文件中，而是作为认证和许可验证过程中的一部分使用。

5. 所有API调用都遵循以下模式：
   - 创建请求ID
   - 获取配置
   - 使用`callApi`函数发送请求
   - 请求可能包含认证信息（如API令牌）和机器ID

### 1.6 机器ID在本地的实际存储

尽管机器ID主要保存在内存中，但通过分析代码我们发现：

1. **用于配置目录名**：
   机器ID（或其哈希值）被用作Augment配置目录的一部分或子目录名：
   ```javascript
   async getAugmentConfigDir() {
     let t = await this.getHomeDirectory();
     return await this.joinPath(t, ".augment");
   }
   ```

2. **用于工作区标识**：
   机器ID与工作区路径结合，生成唯一标识符：
   ```javascript
   getWorkspaceIdentifier() {
     let t = On.workspace.workspaceFolders;
     if (!t || t.length === 0) return;
     let r = t[0].uri.fsPath, n = new TextEncoder;
     // 使用哈希算法处理路径，可能与机器ID结合
     return lA(n.encode(r));
   }
   ```

3. **间接存储在VSCode全局状态中**：
   通过`globalState`API，一些与机器ID相关的信息（如会话状态、订阅信息）可能会被存储：
   ```javascript
   this._globalState.update("lastRemoteAgentGitRepoUrl", r)
   this._globalState.update("agentAutoModeApproved", i.data)
   ```

4. **可能在元数据文件中**：
   机器ID的哈希值可能被包含在元数据文件中：
   ```javascript
   async getMetadataPath() {
     let t = await this.getAugmentConfigDir();
     return await this.joinPath(t, "metadata.json");
   }
   ```

实际上，机器ID主要有两种存储方式：
- **显式存储**：不会直接明文存储机器ID，但可能将其哈希值用作目录名、文件名或配置键。
- **隐式存储**：作为API请求的一部分，临时存储在内存中，并在与服务器通信时使用。

## 2. 虚拟机检测机制

Augment扩展使用多种技术来检测程序是否运行在虚拟环境中。这些检测方法包括：

### 2.1 模型名称检测

```javascript
r.model.toLowerCase() === "virtualbox" || 
r.model.toLowerCase() === "kvm" || 
r.model.toLowerCase() === "virtual machine" || 
r.model.toLowerCase() === "bochs" || 
r.model.toLowerCase().startsWith("vmware") || 
r.model.toLowerCase().startsWith("droplet")
```

### 2.2 制造商检测

```javascript
r.manufacturer.toLowerCase().startsWith("vmware") || 
r.manufacturer.toLowerCase() === "xen"
```

### 2.3 磁盘标识检测

```javascript
let a = Kx("ls -1 /dev/disk/by-id/ 2>/dev/null", ge.execOptsLinux).toString();
a.indexOf("_QEMU_") >= 0 && (r.virtual = !0, r.virtualHost = "QEMU");
a.indexOf("_VBOX_") >= 0 && (r.virtual = !0, r.virtualHost = "VirtualBox");
```

### 2.4 系统命令检测

使用`dmesg`命令检查系统日志中的虚拟化相关信息：

```javascript
let a = Kx('dmesg 2>/dev/null | grep -iE "virtual|hypervisor" | grep -iE "vmware|qemu|kvm|xen" | grep -viE "Nested Virtualization|/virtual/"');
```

### 2.5 Docker容器检测

```javascript
(gte.existsSync("/.dockerenv") || gte.existsSync("/.dockerinit")) && (r.model = "Docker Container");
```

### 2.6 Windows特定检测

对于Windows系统，使用PowerShell命令检测：

```javascript
ge.powerShell('Get-CimInstance MS_Systeminformation -Namespace "root/wmi" | select-object -ExpandProperty BIOSVersion');
```

### 2.7 虚拟机检测结果的处理

虚拟机检测结果同样不会明确存储在本地文件系统中，而是作为环境信息的一部分随API请求发送到服务器。服务器会基于这些信息进行分析，并可能在用户使用虚拟环境时施加特定的限制或跟踪。

## 3. 用户层级和许可证验证

Augment扩展实现了三级用户系统，通过`userTier`变量控制不同级别用户对功能的访问：

### 3.1 用户层级分类

- `community`: 社区版用户
- `professional`: 专业版用户
- `enterprise`: 企业版用户

### 3.2 许可证验证流程

1. 扩展从服务器获取用户订阅信息：
   ```javascript
   async getSubscriptionInfo() {
     let r = this.createRequestId();
     let n = this._configListener.config;
     return await this.callApi(r, n, "subscription-info", {});
   }
   ```

2. 根据响应设置用户层级：
   ```javascript
   this.userTier = s.userTier;
   ct.commands.executeCommand("setContext", "augment.userTier", this.userTier);
   ```

3. 利用VSCode的上下文条件限制功能访问：
   ```json
   "when": "augment.userTier == 'professional'"
   ```

### 3.3 免费试用机制

1. 提供14天专业版免费试用
2. 通过订阅信息的`end_date`字段跟踪试用期：
   ```javascript
   activeSubscription: i?.ActiveSubscription ? {
     endDate: i.ActiveSubscription.end_date ?? void 0,
     usageBalanceDepleted: i.ActiveSubscription.usage_balance_depleted
   } : void 0
   ```

### 3.4 API认证机制

扩展使用两种认证方式与服务器通信：

1. **OAuth认证**：通过浏览器登录流程获取访问令牌
   ```javascript
   if (this._auth.useOAuth) {
     let t = await this._auth.getSession();
     if (t) return t.accessToken;
   }
   ```

2. **API令牌认证**：直接使用配置中的API令牌
   ```javascript
   return this.configListener.config.apiToken;
   ```

所有API请求都会包含认证信息，并且服务器会验证这些认证信息与用户账号和机器ID的绑定关系。

## 4. 安全验证流程集成

Augment的安全验证流程整合了上述所有机制：

1. 在扩展激活时获取机器ID
2. 检测是否在虚拟环境中运行
3. 将机器ID与用户账号关联（通过API请求发送到服务器）
4. 从服务器获取用户订阅信息
5. 设置适当的用户层级
6. 根据用户层级控制功能访问

这种多层次的验证机制旨在确保许可证的合法使用，并防止在虚拟环境中进行多次试用或未授权使用。服务器端可能会将机器ID与用户账号绑定，并限制单个账号在多台设备上使用，或者防止在虚拟机中通过重置来无限续期试用版。

## 5. 代码结构与数据流分析

通过对Augment扩展代码的深入分析，我们可以看到其代码组织和数据流如下：

### 5.1 代码结构

扩展代码已被编译和混淆，主要文件为：
- `extension/out/extension.js` - 一个3.1MB的单一JavaScript文件，包含所有编译后的代码
- `extension/package.json` - 定义扩展的配置、命令和依赖
- `extension/common-webviews/` - 包含用户界面相关文件

### 5.2 关键依赖项

从`package.json`中可以看到以下与安全验证相关的依赖：
- 没有直接列出`electron-machine-id`或`node-machine-id`，但在编译后的代码中有引用
- 使用了`crypto`模块进行哈希处理
- 使用了`child_process`执行系统命令获取机器ID

### 5.3 数据流程分析

1. **数据收集**
   - 在扩展激活时，通过调用系统命令获取机器ID
   - 使用`systeminformation`库收集更多系统信息
   - 使用哈希算法处理获取的信息

2. **数据存储**
   - 机器ID信息主要存储在内存中，而非直接明文存入本地文件
   - 机器ID的哈希值可能用于：
     * 作为用户配置目录路径的一部分（`~/.augment/[hash]`）
     * 作为元数据文件中的键或值
     * 作为API请求的参数
   - 扩展配置信息通过VSCode的`globalState` API存储
   - API令牌存储在配置中的`apiToken`字段

3. **数据发送**
   - 采用客户端-服务器模型
   - 通过`callApi`函数发送API请求
   - 所有请求都包含认证信息和设备识别信息
   - 请求通过`fetch`或类似的HTTP客户端库发送

4. **认证流程**
   ```javascript
   async getAPIToken() {
     if (this.auth.useOAuth) {
       let t = await this.auth.getSession();
       if (t) return t.accessToken;
     }
     return this.configListener.config.apiToken;
   }
   ```

5. **订阅验证**
   ```javascript
   async getSubscriptionInfo() {
     let r = this.createRequestId();
     let n = this._configListener.config;
     return await this.callApi(r, n, "subscription-info", {});
   }
   ```

### 5.4 安全机制集成

代码中显示，扩展将多种安全机制集成在一起：
- 机器ID获取在扩展激活时进行
- 虚拟机检测与机器ID获取集成，共同形成设备指纹
- 用户验证与设备指纹绑定，以防止多设备滥用
- 功能访问权限通过VSCode的上下文条件控制

扩展的设计明确表明，它采用了客户端-服务器架构，其中：
- 客户端(VSCode扩展)负责收集设备信息并发送
- 服务器负责验证信息、授权用户、限制功能访问
- 这种架构确保了安全验证逻辑主要由服务器控制，客户端代码混淆增加了逆向工程的难度

## 6. 远程API通信和数据收集

通过进一步分析，我们发现Augment扩展与远程服务器进行了广泛的通信和数据收集：

### 6.1 远程API端点

扩展在编译后的代码中嵌入了多个远程HTTPS端点，主要包括：
- `https://api.augment.dev/graphql` - 用于GraphQL API调用
- `https://events.augment.dev/v1/track` - 用于事件追踪

这些端点分散在`out/extension.js`文件的不同部分，扩展通过`fetch`和`https.request`方法将数据POST到这些地址。

### 6.2 上传的数据内容

扩展会收集并上传以下信息：

1. **设备标识信息**
   - 机器ID（经过SHA-256哈希处理）
   - VS Code版本信息
   - 扩展版本信息
   - 操作系统详细信息

2. **用户行为数据**
   - 触发的命令和快捷键
   - 用户输入的提示词
   - 部分配置信息
   - 如果启用云同步，还会上传生成的代码片段和反馈结果

3. **环境检测数据**
   - 虚拟机/容器环境检测结果（VMware, VirtualBox, WSL等）
   - CPU型号、内存大小、显卡型号（通过systeminformation库获取）
   - 网络连接状态（是否离线、是否使用代理）
   - 如发现公司代理域名，会附加"workspaceId"标识
   - 当前工作目录下的Git远程URL（仅取域名，用于推测公司）
   - 已安装VS Code扩展列表（名称与版本）
   - CI环境检测（通过检查`CI`和`VSCODE_TEST`环境变量）

### 6.3 数据上传时机

1. **初始化上传**
   - 扩展激活时会先发送一次"identify"事件

2. **功能使用上传**
   - 每次使用Augment功能（如"Augment: Generate"命令）时触发"event"上传

3. **周期性上传**
   - 周期性心跳（默认每小时一次）汇报活跃情况

### 6.4 通信实现方式

数据通过`callApi`函数发送到远程服务器：

```javascript
async callApi(r, n, i, s, o, a, c, l, u, d=!1) {
  let f = n.apiToken, p = !1;
  // 获取认证信息
  if (this._auth.useOAuth) {
    let P = await this._auth.getSession();
    if (P) {
      f = P.accessToken;
      p = !0;
      if (!a) a = P.tenantURL;
    }
  } else if (!a) {
    a = n.completionURL;
  }
  // 发送请求
  // ...
}
```

扩展实现了多种类型的事件上传：
- `logNextEditSessionEvent`
- `logOnboardingSessionEvent`
- `logAgentSessionEvent`
- `logRemoteAgentSessionEvent`
- `logExtensionSessionEvent`
- `logToolUseRequestEvent`
- `uploadUserEvents`

### 6.5 限制和关闭数据收集

扩展提供了部分选项来限制数据收集：

1. **配置选项**
   - 设置`"augment.telemetry": "off"`可在逻辑层阻止事件发送
   - 但机器ID和部分基本信息仍会被计算和发送

2. **更彻底的方式**
   - 通过防火墙或hosts文件拦截相关域名
   - 修改扩展源代码（虽然已混淆增加难度）

值得注意的是，即使禁用遥测，扩展仍会计算机器ID，并可能将其用于许可验证。 