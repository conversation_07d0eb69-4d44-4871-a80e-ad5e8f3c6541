import{S as c,i as o,s as i,b as n,c as e,e as u,f as d,n as r,h as f}from"./SpinnerAugment-BGEGncoZ.js";function h(C){let s,l;return{c(){s=n("svg"),l=n("path"),e(l,"fill-rule","evenodd"),e(l,"clip-rule","evenodd"),e(l,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),e(l,"fill","currentColor"),e(s,"class",C[0]),e(s,"width","15"),e(s,"height","15"),e(s,"viewBox","0 0 15 15"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(t,a){u(t,s,a),d(s,l)},p(t,[a]){1&a&&e(s,"class",t[0])},i:r,o:r,d(t){t&&f(s)}}}function p(C,s,l){let{class:t=""}=s;return C.$$set=a=>{"class"in a&&l(0,t=a.class)},[t]}class w extends c{constructor(s){super(),o(this,s,p,h,i,{class:0})}}export{w as O};
