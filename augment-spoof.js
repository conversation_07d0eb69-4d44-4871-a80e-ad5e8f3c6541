/**
 * augment-spoof.js - Augment VSCode扩展验证规避脚本
 * 
 * 该脚本通过修改Augment扩展的会话ID和其他验证机制，帮助规避许可证验证
 */

// 打印调试信息
console.log('[augment-spoof] 脚本已加载');

// 当所有扩展都激活后再 patch，避免时序问题
const { extensions } = require('vscode');
const crypto = require('crypto');
const os = require('os');

/**
 * 生成一个持久化的随机ID
 * 基于当前机器的某些属性生成，但不使用真实的机器ID
 */
function generatePersistentId() {
  try {
    // 使用计算机名称和用户名作为基础，但不使用真实机器ID
    const base = `${os.hostname()}-${os.userInfo().username}`;
    // 使用SHA-256哈希算法
    const hash = crypto.createHash('sha256');
    hash.update(base);
    return hash.digest('hex');
  } catch (e) {
    console.error('[augment-spoof] 生成持久化ID失败:', e);
    // 回退到随机UUID
    return crypto.randomUUID?.() || Date.now().toString();
  }
}

/**
 * 尝试拦截electron-machine-id模块
 */
function patchMachineId() {
  try {
    // 尝试找到模块缓存中的electron-machine-id
    const moduleCache = require.cache;
    const persistentId = generatePersistentId();

    // 遍历模块缓存，寻找electron-machine-id相关模块
    for (const key in moduleCache) {
      if (key.includes('machine-id') || 
          key.includes('machineId') || 
          moduleCache[key]?.exports?.machineIdSync) {
        
        const mod = moduleCache[key];
        console.log(`[augment-spoof] 找到可能的machine-id模块: ${key}`);
        
        // 如果模块导出了machineIdSync和machineId方法，替换它们
        if (mod.exports?.machineIdSync) {
          const originalSync = mod.exports.machineIdSync;
          mod.exports.machineIdSync = function() {
            console.log('[augment-spoof] 拦截了machineIdSync调用');
            return persistentId;
          };
          console.log('[augment-spoof] 成功替换machineIdSync方法');
        }
        
        if (mod.exports?.machineId) {
          const originalAsync = mod.exports.machineId;
          mod.exports.machineId = function() {
            console.log('[augment-spoof] 拦截了machineId调用');
            return Promise.resolve(persistentId);
          };
          console.log('[augment-spoof] 成功替换machineId方法');
        }
      }
    }
    
    // 尝试直接修改electron-machine-id模块
    try {
      const machineId = require('electron-machine-id');
      if (machineId) {
        if (machineId.machineIdSync) {
          machineId.machineIdSync = () => persistentId;
          console.log('[augment-spoof] 直接修改了electron-machine-id.machineIdSync');
        }
        if (machineId.machineId) {
          machineId.machineId = () => Promise.resolve(persistentId);
          console.log('[augment-spoof] 直接修改了electron-machine-id.machineId');
        }
      }
    } catch (err) {
      console.log('[augment-spoof] 直接修改electron-machine-id失败 (可能未安装):', err.message);
    }
    
    return persistentId;
  } catch (e) {
    console.error('[augment-spoof] 修补machineId失败:', e);
    return null;
  }
}

// 监听扩展变化事件
extensions.onDidChange(() => {
  try {
    // 获取Augment扩展
    const aug = extensions.getExtension('augment.vscode-augment');
    if (!aug || !aug.isActive) return;

    // 获取API服务器实例
    const api = aug.exports?._apiServer;
    if (!api || api.__patched) return;

    console.log('[augment-spoof] Augment扩展已激活，开始修补');

    // 生成持久化ID
    const persistentId = generatePersistentId();
    console.log('[augment-spoof] 生成的持久化ID:', persistentId);

    // 修补sessionId
    Object.defineProperty(api, 'sessionId', {
      get() { 
        console.log('[augment-spoof] sessionId被读取，返回修改后的值');
        return persistentId; 
      },
      set() { 
        console.log('[augment-spoof] 尝试设置sessionId，已忽略');
      },
      configurable: false
    });

    // 尝试修补machineId
    patchMachineId();

    // 标记已修补
    api.__patched = true;
    console.log('[augment-spoof] 成功修补API服务器');
  } catch (e) {
    console.error('[augment-spoof] 修补失败:', e);
  }
});

// 当扩展已加载时初始化
setTimeout(() => {
  try {
    const aug = extensions.getExtension('augment.vscode-augment');
    if (aug && aug.isActive) {
      console.log('[augment-spoof] Augment扩展已经处于活动状态，立即修补');
      const event = new Event('change');
      extensions.onDidChange.emit(event);
    } else {
      console.log('[augment-spoof] Augment扩展尚未激活，等待激活事件');
    }
  } catch (e) {
    console.error('[augment-spoof] 初始检查失败:', e);
  }
}, 2000); // 延迟2秒后检查 