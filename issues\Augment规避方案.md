# Augment 验证规避方案

## 基本原理

Augment VSCode扩展使用多种方法验证许可证和检测环境：

1. **会话ID识别**：每次启动生成一个`sessionId`，发送到服务器
2. **机器ID验证**：通过`electron-machine-id`库获取设备唯一标识符
3. **虚拟环境检测**：检查系统是否运行在虚拟机/容器中

## 规避思路

我们的方案主要通过替换会话ID来规避验证：

1. **优点**：
   - 实现简单，只需修改一个属性
   - 不依赖外部工具或复杂设置
   - 不需要持续运行代理服务器

2. **局限性**：
   - 未处理机器ID检测（但可能已足够）
   - 未处理虚拟机检测

## 具体实现

核心实现是监听扩展激活，然后覆盖`_apiServer.sessionId`的getter方法，返回一个固定的随机ID。

```javascript
// augment-spoof.js
const { extensions } = require('vscode');
extensions.onDidChange(() => {
  const aug = extensions.getExtension('augment.vscode-augment');
  if (!aug || !aug.isActive) return;
  
  try {
    const api = aug.exports?._apiServer;
    if (!api || api.__patched) return;
    
    // 生成随机ID或使用固定ID
    const crypto = require('crypto');
    const newId = crypto.randomUUID?.() || 
      ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,
        c => (c ^ crypto.randomBytes(1)[0] & 15 >> c/4).toString(16));

    // 覆盖sessionId的getter
    Object.defineProperty(api, 'sessionId', {
      get() { return newId; },
      set() {}, // 忽略设置操作
      configurable: false // 防止被再次修改
    });
    
    api.__patched = true;
    console.log('[augment-spoof] sessionId =>', newId);
  } catch (e) {
    console.error('[augment-spoof] patch failed:', e);
  }
});
```

## 使用方法

1. 将脚本保存到VSCode扩展目录
2. 在VSCode启动时加载此脚本
3. 脚本会自动监听扩展激活并修改会话ID 